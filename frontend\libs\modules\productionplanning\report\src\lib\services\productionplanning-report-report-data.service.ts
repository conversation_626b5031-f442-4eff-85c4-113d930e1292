/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';

import { DataServiceFlatRoot, ServiceRole, IDataServiceOptions, IDataServiceEndPointOptions, IDataServiceRoleOptions, IEntityList } from '@libs/platform/data-access';
import { IReportEntity, ReportComplete } from '../model/models';
import { PpsReportProcessorService } from './processors/pps-report-processor.service';


@Injectable({
	providedIn: 'root',
})
export class ProductionplanningReportReportDataService extends DataServiceFlatRoot<IReportEntity, ReportComplete> {
	private reportProcessor!: PpsReportProcessorService<IReportEntity>;

	/**
	 * Constructs the service and registers the report processor.
	 */
	public constructor() {
		const reportProcessor = new PpsReportProcessorService<IReportEntity>();
		const options: IDataServiceOptions<IReportEntity> = {
			apiUrl: 'productionplanning/report/report',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'filtered',
				usePost: true,
			},
			deleteInfo: <IDataServiceEndPointOptions>{
				endPoint: 'multidelete',
			},
			createInfo: <IDataServiceEndPointOptions>{
				endPoint: 'create',
			},
			updateInfo: <IDataServiceEndPointOptions>{
				endPoint: 'update',
			},
			roleInfo: <IDataServiceRoleOptions<IReportEntity>>{
				role: ServiceRole.Root,
				itemName: 'Reports',
			},
			processors: [reportProcessor],
			entityActions: { createSupported: false, deleteSupported: true },
		};

		super(options);
		this.reportProcessor = reportProcessor;
		this.reportProcessor.setDataService(this);
	}

	/**
	 * Updates the entity list with the reports from the complete object.
	 * @param complete The ReportComplete object containing updated reports.
	 * @param entityList The entity list to update.
	 */
	protected takeOverUpdatedFromComplete(complete: ReportComplete, entityList: IEntityList<IReportEntity>) {
		if (complete && complete.Reports && complete.Reports.length > 0) {
			entityList.updateEntities(complete.Reports);
		}
	}

	/**
	 * Creates a ReportComplete update entity from a modified report entity.
	 * @param modified The modified IReportEntity or null.
	 * @returns The created ReportComplete object.
	 */
	public override createUpdateEntity(modified: IReportEntity | null): ReportComplete {
		const complete = new ReportComplete();
		if (modified !== null) {
			complete.MainItemId = modified.Id;
			complete.Reports = [modified];
		}

		return complete;
	}

	/**
	 * Gets the modifications from the ReportComplete object.
	 * @param complete The ReportComplete object.
	 * @returns An array of IReportEntity modifications.
	 */
	public override getModificationsFromUpdate(complete: ReportComplete): IReportEntity[] {
		if (complete.Reports === null) {
			complete.Reports = [];
		}

		return complete.Reports;
	}
}
