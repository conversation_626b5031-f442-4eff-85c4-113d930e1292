/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import { ProcurementInternalModule } from '@libs/procurement/shared';
import { ProcurementCommonUpdateVatGroupService } from '@libs/procurement/common';
import { PesCompleteNew } from '../model/complete-class/pes-complete-new.class';
import { IPesHeaderEntity } from '../model/entities';
import { ProcurementPesHeaderDataService } from './procurement-pes-header-data.service';

/**
 * Procurement pes update vat group service
 */
@Injectable({
	providedIn: 'root'
})
export class ProcurementPesUpdateVatGroupService extends ProcurementCommonUpdateVatGroupService<IPesHeaderEntity, PesCompleteNew> {

	/**
	 * The constructor
	 * @protected
	 */
	protected constructor() {
		const dataService = inject(ProcurementPesHeaderDataService);
		super(dataService, ProcurementInternalModule.Pes);
	}

	/**
	 * Get contractName of customized recalculate logic after vatGroup updated
	 * @protected
	 */
	protected override getContractName(): string {
		return ProcurementInternalModule.Pes;
	}
}