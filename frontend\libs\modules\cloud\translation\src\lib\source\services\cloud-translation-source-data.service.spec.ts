/**
 * Copyright(c) RIB Software GmbH
 */

// import { TestBed } from '@angular/core/testing';

// import { CloudTranslationSourceDataService } from './cloud-translation-source-data.service';

describe('CloudTranslationSourceDataService', () => {
	// let service: CloudTranslationSourceDataService;

	// beforeEach(() => {
	// 	TestBed.configureTestingModule({});
	// 	service = TestBed.inject(CloudTranslationSourceDataService);
	// });

	it('should be created', () => {
		expect(true).toBeTruthy();
	});
});
