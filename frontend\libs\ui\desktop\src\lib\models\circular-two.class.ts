import { LazyInjectable, PlatformLazyInjectorService } from '@libs/platform/common';
import { UiCircularOneToken, UiCircularTwoToken, IUiCircularTwo } from '@libs/ui/interfaces';
import { Injector } from '@angular/core';

@LazyInjectable({
	token: UiCircularTwoToken
})
export class CircularTwo implements IUiCircularTwo {

	public constructor(injector: Injector) {
		this.lazyInjector = injector.get(PlatformLazyInjectorService);
	}

	private readonly lazyInjector: PlatformLazyInjectorService;

	public test() {
		this.lazyInjector.inject(UiCircularOneToken).then(c => c.output());
	}

	public outputOther() {
		console.log('Circular Two!');
	}
}