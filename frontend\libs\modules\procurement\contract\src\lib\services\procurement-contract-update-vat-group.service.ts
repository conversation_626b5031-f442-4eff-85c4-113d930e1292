/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import { ProcurementCommonUpdateVatGroupService } from '@libs/procurement/common';
import { IConHeaderEntity } from '../model/entities';
import { ContractComplete } from '../model/contract-complete.class';
import { ProcurementContractHeaderDataService } from './procurement-contract-header-data.service';

/**
 * Procurement contract update vat group service
 */
@Injectable({
	providedIn: 'root'
})
export class ProcurementContractUpdateVatGroupService extends ProcurementCommonUpdateVatGroupService<IConHeaderEntity, ContractComplete> {

	/**
	 * The constructor
	 * @protected
	 */
	protected constructor() {
		const dataService = inject(ProcurementContractHeaderDataService);
		super(dataService, dataService.getInternalModuleName());
	}
}