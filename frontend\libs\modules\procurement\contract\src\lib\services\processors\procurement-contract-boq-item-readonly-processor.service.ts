/*
 * Copyright(c) RIB Software GmbH
 */

import { ProcurementCommonBoqItemReadonlyProcessor } from '@libs/procurement/common';
import { IConHeaderEntity } from '@libs/procurement/interfaces';
import { ContractComplete } from '../../model/contract-complete.class';
import { ProcurementContractBoqItemDataService } from '../procurement-contract-boq-item-data.service';
import { IBoqItemEntity } from '@libs/boq/interfaces';
import { ProcurementContractHeaderDataService } from '../procurement-contract-header-data.service';
import { isNil } from 'lodash';
import { inject } from '@angular/core';

export class ProcurementContractBoqItemReadonlyProcessor extends ProcurementCommonBoqItemReadonlyProcessor<IConHeaderEntity, ContractComplete> {
	private readonly contractDataService = inject(ProcurementContractHeaderDataService);

	/**
	 * The constructor
	 */
	public constructor(protected contractBoQItemDataService: ProcurementContractBoqItemDataService) {
		super(contractBoQItemDataService);
	}

	protected override isFrameworkContractCallOffByWic(): boolean {
		return this.contractDataService.isFrameworkContractCallOffByWic();
	}

	protected override isChangeOrder(boqItem: IBoqItemEntity): boolean {
		const headerEntity = this.contractDataService.getSelectedEntity();

		//There is one requirement in ticket #97355, change order BoQ item, only quantity field is editable
		//In AngularJs implementation it will load the baseBoqItem. But seems it is not necessary. And it will have performance issue.
		return !isNil(headerEntity?.ContractHeaderFk) && !isNil(boqItem.BoqItemPrjItemFk);

		//For ticket #122993, the Price\PriceOc\Correction\CorrectionOc will be readonly if it is a call off.
		//But such case was already covered by the case here. Now only quantity field is editable.
		//TODO: please double check with PM\tester, whether there is other special cases.
	}
}
