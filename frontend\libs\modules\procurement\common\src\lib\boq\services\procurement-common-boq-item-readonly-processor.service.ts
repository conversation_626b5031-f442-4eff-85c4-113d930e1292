/*
 * Copyright(c) RIB Software GmbH
 */

import { EntityReadonlyProcessorBase, ReadonlyFunctions, ReadonlyInfo } from '@libs/basics/shared';
import { BoqItemConfigService, IBoqParentCompleteEntity, IBoqParentEntity } from '@libs/boq/main';
import { ProcurementCommonBoqItemDataServiceBase } from '../../services/procurement-common-boq-item-data.service';
import { IBoqItemEntity } from '@libs/boq/interfaces';
import { isNil } from 'lodash';
import { inject } from '@angular/core';

export class ProcurementCommonBoqItemReadonlyProcessor<PT extends IBoqParentEntity, PU extends IBoqParentCompleteEntity> extends EntityReadonlyProcessorBase<IBoqItemEntity> {
	private allFields: string[] = [];
	private readonly boqItemLayoutConfigService = inject(BoqItemConfigService);

	/**
	 *The constructor
	 */
	public constructor(protected dataService: ProcurementCommonBoqItemDataServiceBase<PT, PU>) {
		super(dataService);
	}

	public async initializeProcess() {
		const layoutConfig = await this.boqItemLayoutConfigService.getLayoutConfiguration();

		// Extract all field names from the layout configuration groups
		// TODO: the dynamic fields are not included.
		this.allFields = [
			// Fields from groups (attributes and additionalAttributes)
			...(layoutConfig.groups ?? []).flatMap((group) => [...group.attributes.map((attr) => String(attr)), ...(group.additionalAttributes?.map((attr) => String(attr)) || [])]),
			// Transient fields
			...(layoutConfig.transientFields?.map((transientField) => transientField.id) || []),
		];
	}

	//#region methods below should be overrided by subclass. If not override will not process the related readonly fields.
	protected isBudgetFieldsReadonly(): boolean {
		return false;
	}

	protected isFrameworkContractCallOffByWic(): boolean {
		return false;
	}

	protected isChangeOrder(_boqItem: IBoqItemEntity): boolean {
		return false;
	}

	//#endregion

	protected readonlyByFrameworkContractCallOff(info: ReadonlyInfo<IBoqItemEntity>): boolean {
		return !isNil(info.item.BoqItemWicBoqFk) && !isNil(info.item.BoqItemWicItemFk) && this.isFrameworkContractCallOffByWic();
	}

	public generateReadonlyFunctions(): ReadonlyFunctions<IBoqItemEntity> {
		return {
			BudgetPerUnit: {
				shared: ['BudgetFixedUnit', 'BudgetTotal', 'BudgetFixedTotal'],
				readonly: this.isBudgetFieldsReadonly,
			},
			Reference: {
				shared: ['BriefInfo', 'BasUomFk', 'Price', 'PriceOc', 'Pricegross', 'PricegrossOc', 'BasBlobsSpecificationFk'],
				readonly:  this.readonlyByFrameworkContractCallOff,
			},
		};
	}

	protected override readonlyEntity(_item: IBoqItemEntity): boolean {
		return this.dataService.isParentEntityReadonly();
	}

	public override process(toProcess: IBoqItemEntity): void {

		//There is one requirement in ticket #97355, change order BoQ item, only quantity field is editable
		if (this.isChangeOrder(toProcess)) {
			if (this.readonlyEntity(toProcess)) {
				this.runtime.setEntityReadOnly(toProcess, true);
				return;
			}

			// For change orders, make all fields readonly except Quantity
			const state = this.getReadonlyState(toProcess);

			this.allFields.forEach((field) => {
				const isReadonly = field !== 'Quantity';
				this.runtime.setEntityReadOnlyFields(toProcess, [
					{
						field: field,
						readOnly: isReadonly,
					},
				]);

				// Update the readonly state
				state[field] = isReadonly;
			});
		} else {
			// If not a change order, use the parent class logic
			super.process(toProcess);
		}
	}
}
