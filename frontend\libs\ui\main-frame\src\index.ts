/*
 * Copyright(c) RIB Software GmbH
 */

export * from './lib/ui-main-frame.module';
export * from './lib/model/ui-main-frame-module-info.class';

export { ClientAreaBaseComponent } from './lib/components/client-area-base/client-area-base.component';

export { MainFrameComponent } from './lib/main-frame.component';

import { IApplicationModuleInfo } from '@libs/platform/common';
import { UiMainFrameModuleInfo } from './lib/model/ui-main-frame-module-info.class';

/**
 * Returns the module info object for the ui.main-frame module.
 *
 * This function implements the {@link IApplicationModule.getModuleInfo} method.
 * Do not remove it.
 * It may be called by generated code.
 *
 * @return The singleton instance of the module info object.
 *
 * @see {@link IApplicationModule.getModuleInfo}
 */
export function getModuleInfo(): IApplicationModuleInfo {
    return UiMainFrameModuleInfo.instance;
}