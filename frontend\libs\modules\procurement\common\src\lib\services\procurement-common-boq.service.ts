/*
 * Copyright(c) RIB Software GmbH
 */

import { BoqCompositeDataService, IBoqParentCompleteEntity, IBoqParentEntity } from '@libs/boq/main';
import { IDataServiceEndPointOptions, IDataServiceOptions, IDataServiceRoleOptions, ServiceRole } from '@libs/platform/data-access';
import { IPrcBoqExtendedEntity, IPrcPackageEntity } from '@libs/procurement/interfaces';
import { IPrcBoqExtendedComplete, IPrcHeaderDataService } from '../model/interfaces';
import { ProcurementCommonBoqCreateService } from './procurement-common-boq-create.service';
import { inject } from '@angular/core';
import { BasicsSharedProcurementConfigurationLookupService } from '@libs/basics/shared';
import { PlatformHttpService } from '@libs/platform/common';
import { UiCommonMessageBoxService } from '@libs/ui/common';
import { ProcurementCommonCreatePackageService } from './procurement-common-create-package.service';
import { ProcurementCommonBoqReadonlyProcessor } from '../boq/services/procurement-common-boq-readonly-processor.service';
import { ProcurementCommonBoqDataProcessor } from '../boq/services/procurement-common-boq-processor.class';
import { ProcurementCommonBoqValidationService } from '../boq/services/base/procurement-boq-validation.service';

/** Prc common boq list data service */
export class ProcurementCommonBoqDataServiceBase<PT extends IBoqParentEntity, PU extends IBoqParentCompleteEntity> extends BoqCompositeDataService<IPrcBoqExtendedEntity, IPrcBoqExtendedComplete, PT, PU> {
	private readonly packageCreateService = inject(ProcurementCommonCreatePackageService);
	private readonly boqCreateService = inject(ProcurementCommonBoqCreateService);
	private readonly prcConfigurationLookupService = inject(BasicsSharedProcurementConfigurationLookupService);
	public readonly readonlyProcessor: ProcurementCommonBoqReadonlyProcessor<PT, PU>;
	public readonly dataProcessor: ProcurementCommonBoqDataProcessor<PT, PU>;
	private readonly httpService = inject(PlatformHttpService);
	private readonly messageService = inject(UiCommonMessageBoxService);

	public validationService?: ProcurementCommonBoqValidationService<PT, PU>;

	public constructor(protected parentService: IPrcHeaderDataService<PT, PU>) {
		const options: IDataServiceOptions<IPrcBoqExtendedEntity> = {
			apiUrl: 'procurement/common/boq',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list',
				usePost: false,
			},
			roleInfo: <IDataServiceRoleOptions<IPrcBoqExtendedEntity>>{
				role: ServiceRole.Node,
				itemName: 'PrcBoqExtended',
				parent: parentService,
			},
			entityActions: {
				createDynamicDialogSupported: true,
			},
		};
		super(options);

		this.readonlyProcessor = this.createReadonlyProcessor();
		this.dataProcessor = this.createDataProcessor();

		this.processor.addProcessor([this.readonlyProcessor, this.dataProcessor]);

		this.init();

		/*this.parentService.changeStructureSetTaxCodeToItemBoq$.subscribe(e => {
			this.updateTaxFromParent();
		});*/
	}

	protected createReadonlyProcessor() {
		return new ProcurementCommonBoqReadonlyProcessor(this);
	}

	protected createDataProcessor() {
		return new ProcurementCommonBoqDataProcessor(this);
	}

	protected init() {
		this.parentService.readonlyChanged$.subscribe(() => {
			this.updateReadonlyForList();
		});

		this.listChanged$.subscribe(() => {
			this.getList().forEach((item) => this.dataProcessor.process(item));
		});
	}

	private updateReadonlyForList() {
		this.getList().forEach((item) => this.readonlyProcessor.process(item));
	}

	//  region CRUD operations

	// #region

	protected override provideLoadPayload(): object {
		return {
			prcHeaderFk: this.parentService.getSelectedPrcHeaderEntity().Id,
			exchangeRate: this.parentService.getHeaderContext().exchangeRate,
			filterBackups: false,
		};
	}

	public override isParentFn(parentHeader: PT, prcBoq: IPrcBoqExtendedEntity): boolean {
		return this.parentService.getPrcHeaderEntity(parentHeader).Id === prcBoq.PrcBoq?.PrcHeaderFk;
	}

	public override createUpdateEntity(modified: IPrcBoqExtendedEntity): IPrcBoqExtendedComplete {
		return {
			MainItemId: modified.PrcBoq?.PrcHeaderFk,
			PrcBoqExtended: modified,
		} as IPrcBoqExtendedComplete;
	}

	public override registerByMethod(): boolean {
		return true;
	}

	public override registerNodeModificationsToParentUpdate(complete: PU, modified: IPrcBoqExtendedComplete[], deleted: IPrcBoqExtendedEntity[]) {
		if (modified.length > 0) {
			//complete.PrcBoqExtendedToSave = modified;
		}
		if (deleted.length > 0) {
			//complete.PrcBoqExtendedToDelete = deleted;
		}
	}

	public override getSavedEntitiesFromUpdate(complete: PU): IPrcBoqExtendedEntity[] {
		return [];
		//return complete.PrcBoqExtendedToSave ? (complete.PrcBoqExtendedToSave.flatMap(e => e.PrcBoqExtended ? e.PrcBoqExtended : [])) : [];
	}

	public override getSavedCompletesFromUpdate(parentUpdate: PU): IPrcBoqExtendedComplete[] {
		// TODO
		return [];
	}

	protected override isCreateByFixDialogSupported(): boolean {
		return true;
	}

	/**
	 * Indicates whether the package is auto created if the packageFk is not set in the header context.
	 * @returns true if the package is auto created, false otherwise
	 */
	protected isAutoCreatePackage(): boolean {
		return true;
	}

	protected isAutoSavePackage(): boolean {
		//TODO: check the old AngularJs implementation.
		return true;
	}

	protected async hasWicBoQ(): Promise<boolean> {
		return true;
	}

	protected async hasBaseBoQ(): Promise<boolean> {
		return true;
	}

	public getHeaderContext() {
		return this.parentService.getHeaderContext();
	}

	protected async canCreateNewBoq(): Promise<boolean> {
		return true;
	}

	protected async isCreateVersionBoq(): Promise<boolean> {
		return true;
	}

	/**
	 * @deprecated Attention: Only used for backend! Angular logic should not depend on it anymore
	 * @protected
	 */
	public isInPackage(): boolean {
		return false;
	}

	/**
	 * @deprecated Attention: Only used for backend! Angular logic should not depend on it anymore
	 * @protected
	 */
	protected provideTargetModuleName(): string {
		throw new Error('provideTargetModuleName should be implemented in specific module!');
	}

	protected async setNewPackageToHeader(packageEntity: IPrcPackageEntity): Promise<void> {
		//TODO
	}

	protected async checkPackage(): Promise<void> {
		const headerContext = this.parentService.getHeaderContext();
		const isAutoCreatePackage = this.isAutoCreatePackage();
		const isAutoSavePackage = this.isAutoSavePackage();

		if (!headerContext.packageFk) {
			if (isAutoCreatePackage) {
				const packageEntity = await this.packageCreateService.createPackage({
					Id: 0, // required for checking unique code, can not be null
					PrjProjectFk: headerContext.projectFk,
					StructureFk: headerContext.structureFk,
					BasCurrencyFk: headerContext.currencyFk,
					ConfigurationFk: headerContext.prcConfigFk,
					IsAutoSave: isAutoSavePackage,
				});

				if (!packageEntity) {
					throw new Error('Package creation fails!');
				}

				await this.setNewPackageToHeader(packageEntity);
			} else {
				// TODO - check angularjs, package is required
			}
		}
	}

	protected override async createByFixDialog(): Promise<IPrcBoqExtendedEntity> {
		await this.checkPackage();

		// Always save data first before creating procurement boq
		await this.save();

		// Retrieve context to get latest value after creating package and saving
		const headerContext = this.parentService.getHeaderContext();

		let configHeaderFk = undefined;

		if (headerContext.prcConfigFk) {
			const config = await this.prcConfigurationLookupService.getItemByKeyAsync({ id: headerContext.prcConfigFk });
			configHeaderFk = config?.PrcConfigHeaderFk;
		}

		const hasBaseBoq = await this.hasBaseBoQ();
		const hasWicBoq = await this.hasWicBoQ();
		const canCreateNewBoq = await this.canCreateNewBoq();

		if (!headerContext.packageFk) {
			throw new Error('Package is null!');
		}

		if (!hasBaseBoq && !hasWicBoq && !canCreateNewBoq) {
			await this.messageService.showMsgBox('procurement.common.createPrcBoqNoCopy', 'procurement.common.boq.createDialogTitle', 'info');
			throw new Error('Create boq failed');
		}

		const payload = await this.boqCreateService.createProcurementBoQ({
			packageFk: headerContext.packageFk,
			projectFk: headerContext.projectFk,
			structureFk: headerContext.structureFk ?? null,
			clerkPrcFk: headerContext.clerkPrcFk,
			clerkReqFk: headerContext.clerkReqFk,
			prcConfigHeaderFk: configHeaderFk,
			hasBaseBoq: hasBaseBoq,
			hasWicBoq: hasWicBoq,
			canCreateNewBoq: canCreateNewBoq,
			prcHeaderFk: headerContext.prcHeaderFk,
			basCurrencyFk: headerContext.currencyFk,
			targetModuleName: this.provideTargetModuleName(),
			boqWicCatFk: headerContext.boqWicCatFk,
			boqWicCatBoqFk: headerContext.boqWicCatBoqFk,
			contractFk: headerContext.contractFk,
			usedBoqReferences: this.getList().map((e) => e.BoqRootItem.Reference ?? ''),
			boqReference: await this.getUniqueReference(),
			createVersionBoq: await this.isCreateVersionBoq(),
			validationService: this.validationService,
		});

		if (!payload) {
			throw new Error('Create boq failed');
		}

		if (!payload.reference) {
			payload.reference = this.createNextReferenceNumber();
		}

		return payload as unknown as IPrcBoqExtendedEntity;
	}

	protected override onCreateSucceeded(created: object): IPrcBoqExtendedEntity {
		return created as IPrcBoqExtendedEntity;
	}

	protected async getUniqueReference(): Promise<string> {
		const headerContext = this.parentService.getHeaderContext();

		if (!headerContext.packageFk) {
			return '';
		}

		return this.httpService.get<string>('procurement/common/boq/getuniquereference', {
			params: {
				prcHeaderFk: headerContext.prcHeaderFk,
				packageFk: headerContext.packageFk,
			},
		});
	}

	protected async save(): Promise<void> {
		return;
	}

	/**
	 * Calculates VAT and VAT Original Currency values for BOQ root items
	 * @param boqEntity - The BOQ data to process
	 */
	public calculateVatAndVatOc(boqEntity: IPrcBoqExtendedEntity | IPrcBoqExtendedEntity[]): void {
		const dataArray = Array.isArray(boqEntity) ? boqEntity : [boqEntity];

		dataArray.forEach((data) => {
			data.Vat = data.BoqRootItem.Finalgross - data.BoqRootItem.Finalprice;
			data.VatOc = data.BoqRootItem.FinalgrossOc - data.BoqRootItem.FinalpriceOc;
		});
	}

	public isParentEntityReadonly(){
		return this.parentService.getHeaderContext().readonly;
	}

	// #endregion
	//  endregion

	/*private updateTaxFromParent() {
		const parentSelected = this.parentService.getSelectedEntity();
		if (!parentSelected) {
			return;
		}
		const prcItems = this.getList();
		prcItems.forEach((item) => {
			//item.BoqRootItem.MdcTaxCodeFk = parentSelected.TaxCodeFk;
			this.setModified(item);
		});
	}*/
}
