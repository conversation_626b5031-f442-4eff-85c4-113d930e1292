/*
 * Copyright(c) RIB Software GmbH
 */

export * from './lib/ui-common.module';
export * from './lib/model/ui-common-module-info.class';

// domain controls
export * from './lib/domain-controls';

// form
export * from './lib/form';

// grid
export * from './lib/grid';

// fields
export * from './lib/model/fields';
export * from './lib/services/ui-field-helper.service';

// script editor
export * from './lib/script-editor';

// lookup
export * from './lib/lookup';

// accordion
export * from './lib/accordion';

// dialog framework
export * from './lib/dialogs';

// toolbar
export { ToolbarWrapper } from './lib/model/toolbar/toolbar-wrapper/toolbar-wrapper.class';
export { IToolbar } from './lib/model/toolbar/interface/toolbar.interface';
export { ToolbarComponent } from './lib/components/toolbar/toolbar.component';

// menulist
export * from './lib/services/menu-list/menu-list.interface';
export * from './lib/model/menu-list/interface/index';
export * from './lib/components/menu-list/menu-list/menu-list.component';
export * from './lib/components/menu-list/menu-list-radio/menu-list-radio.component';
export * from './lib/model/menu-list/menu-list-content.class';
export * from './lib/model/toolbar/enum/insert-position.enum';

// overlays
export * from './lib/services/overlays/info-overlay.interface';

// popup
export * from './lib/popup';

// others
export { CloudDesktopSvgIconService } from './lib/services/desktop-svgicon/cloud-desktop-svg-icon.service';
export { CloudDesktopTestService } from './lib/test-dialog/services/cloud-desktop-test.service';

export * from './lib/components/dropdown-button/model/interfaces/dropdown-button.model';
export * from './lib/components/dropdown-button/components/dropdown-button/dropdown-button.component';
export * from './lib/model/script/codemirror-language-modes.enum';
export * from './lib/model/script/codemirror-editor-options.interface';
export * from './lib/model/menu-list/enum/menulist-item-type.enum';
export * from './lib/model/fields/lookup-field.interface';

// resize observer
export * from './lib/model/resize-observer/resize-size.interface';
export * from './lib/model/resize-observer/resize-args.interface';
export * from './lib/model/resize-observer/resize-handler.interface';
export * from './lib/model/resize-observer/resize-options.interface';
export * from './lib/model/resize-observer/resize-messenger.interface';

//navigation
export * from './lib/navigation';

// chart
export * from './lib/chart';

//image select
export * from './lib/mock-data/image-select/image-select-list';

//drag drop
export * from './lib/directives/drag-drop-target.directive';
export * from './lib/directives/drag-drop-file.directive';
export * from './lib/model/file-drop-config.interface';
export * from './lib/services/file-drop.service';
//menu tab
export * from './lib/components/menu-tab/menu-tab.component';
export * from './lib/model/menu-tab/interface/menu-tab-param.interface';

export * from './lib/model/text-display/enums/text-display-type.enum';

//report print
export * from './lib/report-print';

// DOM utilities

export * from './lib/model/dom/css-utils.model';

export * from './lib/components/non-module-page/non-module-page-base.component';
export * from './lib/selection-statement-editor/models/filter-script-def-provider';
export * from './lib/selection-statement-editor/models/interfaces/filter-script-editor-option.interface';
export * from './lib/model/fields/field-type.enum';

//Info bar
export * from './lib/components/infobar/component/info-bar.component';
export * from './lib/components/infobar/model/interfaces/info-bar.interfaces';

export * from './lib/text-editor/model/interfaces/editor-options.interface';
export * from './lib/text-editor/model/interfaces/variable-list.interface';
export * from './lib/services/reporting-print.service';

export { RuleEditorComponent } from './lib/rule-editor/components/rule-editor/rule-editor.component';
export { ExpressionGroupComponent} from './lib/rule-editor/components/expression-group/expression-group.component';
export { Expression } from  './lib/rule-editor/model/data/expression.class';
export { ExpressionGroup } from './lib/rule-editor/model/data/expression-group.class';

export * from './lib/components/tabs-wrapper/tabs-wrapper.component';
export * from './lib/rule-editor/model/representation/dd-state-config.interface';

export * from './lib/domain-controls/services/gantt-control.service';

import { IApplicationModuleInfo } from '@libs/platform/common';
import { UiCommonModuleInfo } from './lib/model/ui-common-module-info.class';

/**
 * Returns the module info object for the ui.common module.
 *
 * This function implements the {@link IApplicationModule.getModuleInfo} method.
 * Do not remove it.
 * It may be called by generated code.
 *
 * @return The singleton instance of the module info object.
 *
 * @see {@link IApplicationModule.getModuleInfo}
 */
export function getModuleInfo(): IApplicationModuleInfo {
    return UiCommonModuleInfo.instance;
}
