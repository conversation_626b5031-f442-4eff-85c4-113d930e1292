/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import { IPrcPackageEntity } from '@libs/procurement/interfaces';
import { ProcurementPackageHeaderDataService } from '../services/package-header-data.service';
import { ProcurementCommonUpdateVatGroupService } from '@libs/procurement/common';
import { PrcPackageCompleteEntity } from '../model/entities/package-complete-entity.class';
import { Package2HeaderDataService } from './package-2header-data.service';

/**
 * Procurement package update vat group service
 */
@Injectable({
	providedIn: 'root'
})
export class ProcurementPackageUpdateVatGroupService extends ProcurementCommonUpdateVatGroupService<IPrcPackageEntity, PrcPackageCompleteEntity> {
	protected readonly package2headerService = inject(Package2HeaderDataService);

	/**
	 * The constructor
	 * @protected
	 */
	protected constructor() {
		const dataService = inject(ProcurementPackageHeaderDataService);
		super(dataService, dataService.getInternalModuleName());
	}

	/**
	 * Get prcHeaderIds
	 * Package maybe has multiple prcHeaderId
	 */
	protected override getPrcHeaderIds(entity: IPrcPackageEntity): number[] {
		const package2HeaderEntities = this.package2headerService.getList();
		return package2HeaderEntities?.filter(e => e.PrcPackageFk === entity.Id)?.map(e => e.PrcHeaderFk) ?? [];
	}
}