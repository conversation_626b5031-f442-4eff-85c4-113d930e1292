/*
 * Copyright(c) RIB Software GmbH
 */

import { LazyInjectableInfo, ModuleInfoBase } from '@libs/platform/common';
import { LAZY_INJECTABLES } from './lazy-injectable-info.model';

export class UiSidebarModuleInfo extends ModuleInfoBase {

	public static readonly instance = new UiSidebarModuleInfo();

	private constructor() {
		super();
	}

	public override get internalModuleName(): string {
		return 'ui.sidebar';
	}

	public override get preloadedTranslations(): string[] {
		return [
			'cloud.desktop',
		];
	}

	public override get lazyInjectables(): LazyInjectableInfo[] {
		return LAZY_INJECTABLES;
	}
}
