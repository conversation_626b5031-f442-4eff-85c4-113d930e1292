
export * from './lib/ui-desktop.module';
export * from './lib/models/ui-desktop-module-info.class';
export * from './lib/models/circular-two.class';

import { IApplicationModuleInfo } from '@libs/platform/common';
import { UiDesktopModuleInfo } from './lib/models/ui-desktop-module-info.class';

/**
 * Returns the module info object for the ui.desktop module.
 *
 * This function implements the {@link IApplicationModule.getModuleInfo} method.
 * Do not remove it.
 * It may be called by generated code.
 *
 * @return The singleton instance of the module info object.
 *
 * @see {@link IApplicationModule.getModuleInfo}
 */
export function getModuleInfo(): IApplicationModuleInfo {
    return UiDesktopModuleInfo.instance;
}
