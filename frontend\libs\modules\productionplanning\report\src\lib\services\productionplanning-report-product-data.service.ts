/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';

import { ProductionplanningReportReportDataService } from './productionplanning-report-report-data.service';
import { IPpsProductEntityGenerated } from '@libs/productionplanning/shared';
import { IReportEntity, Report2ProductComplete, ReportComplete } from '../model/models';
import { IIdentificationData } from '@libs/platform/common';
import { DataServiceFlatLeaf, IDataServiceChildRoleOptions, IDataServiceEndPointOptions, IDataServiceOptions, ServiceRole } from '@libs/platform/data-access';

@Injectable({
	providedIn: 'root',
})
export class ProductionplanningReportProductDataService extends DataServiceFlatLeaf<IPpsProductEntityGenerated, Report2ProductComplete, ReportComplete> {

	public constructor(parentService: ProductionplanningReportReportDataService) {
		const options: IDataServiceOptions<IPpsProductEntityGenerated> = {
			apiUrl: 'productionplanning/report/report2product',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list',
				prepareParam: (ident: IIdentificationData) => {
					return { ReportFk: ident.pKey1 };
				},
				usePost: false,
			},
			roleInfo: <IDataServiceChildRoleOptions<IPpsProductEntityGenerated, IReportEntity, ReportComplete>>{
				role: ServiceRole.Leaf,
				itemName: 'Report2Product',
				parent: parentService,
				parentFilter: 'ReportFk',
			},
			entityActions: { createSupported: false, deleteSupported: false },
		};

		super(options);
	}
}
