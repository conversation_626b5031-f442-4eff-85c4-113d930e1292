/*
 * Copyright(c) RIB Software GmbH
 */


import { EntityReadonlyProcessorBase, ReadonlyFunctions } from '@libs/basics/shared';
import { IPrcBoqExtendedEntity } from '@libs/procurement/interfaces';
import { ProcurementCommonBoqDataServiceBase } from '../../services/procurement-common-boq.service';
import { IBoqParentCompleteEntity, IBoqParentEntity } from '@libs/boq/main';


export class ProcurementCommonBoqReadonlyProcessor<PT extends IBoqParentEntity, PU extends IBoqParentCompleteEntity> extends EntityReadonlyProcessorBase<IPrcBoqExtendedEntity> {

	/**
	 *The constructor
	 */
	public constructor(protected dataService: ProcurementCommonBoqDataServiceBase<PT, PU>) {
		super(dataService);
	}

	public generateReadonlyFunctions(): ReadonlyFunctions<IPrcBoqExtendedEntity> {
		return {
			'Id': {
				shared: [
					'BoqHeader.BoqStatusFk',
					'BoqRootItem.Reference',
					'BoqRootItem.BriefInfo',
					'BoqRootItem.Finalprice',
					'BoqRootItem.FinalpriceOc',
					'BoqHeader.BasCurrencyFk',
					'PrcBoq.PackageFk',
					'BoqRootItem.rcStructureFk',
					'Vat',
					'VatOc',
					'BoqRootItem.Finalgross',
					'BoqRootItem.FinalgrossOc',
				],
				readonly: () => true,
			},
		};
	}

	protected override readonlyEntity(_item: IPrcBoqExtendedEntity): boolean {
		return this.dataService.isParentEntityReadonly();
	}
}
