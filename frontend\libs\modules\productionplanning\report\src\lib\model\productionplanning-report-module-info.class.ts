/*
 * Copyright(c) RIB Software GmbH
 */

import { BusinessModuleInfoBase, DataTranslationGridComponent, EntityInfo } from '@libs/ui/business-base';
import { PRODUCTIONPLANNING_REPORT_REPORT_ENTITY_INFO } from './productionplanning-report-report-entity-info.model';
import { ContainerDefinition, ContainerTypeRef, IContainerDefinition } from '@libs/ui/container-system';
import { DrawingContainerDefinition } from '@libs/model/shared';
import { PRODUCTIONPLANNING_REPORT_PRODUCT_ENTITY_INFO } from './productionplanning-report-product-entity-info.model';
import { PRODUCTIONPLANNING_REPORT_TIME_SHEET_ENTITY_INFO } from './productionplanning-report-time-sheet-entity-info.model';
import { PRODUCTIONPLANNING_REPORT_COST_CODE_ENTITY_INFO } from './productionplanning-report-cost-code-entity-info.model';
import { PRODUCTIONPLANNING_REPORT_DOCUMENT_ENTITY_INFO } from './productionplanning-report-document-entity-info.model';
import { PRODUCTIONPLANNING_REPORT_DOCUMENT_REVISION_ENTITY_INFO } from './productionplanning-report-document-revision-entity-info.model';
import { PPS_REPORT_FORM_DATA_ENTITY_INFO } from './pps-report-form-data-entity-info.model';
import { ProductionPlanningReportProductFilterComponent } from '../components/product-filter.component';

/**
 * The module info object for the `productionplanning.report` content module.
 */
export class ProductionplanningReportModuleInfo extends BusinessModuleInfoBase {
	private static _instance?: ProductionplanningReportModuleInfo;

	/**
	 * Returns the singleton instance of the class.
	 *
	 * @return The singleton instance.
	 */
	public static get instance(): ProductionplanningReportModuleInfo {
		if (!this._instance) {
			this._instance = new ProductionplanningReportModuleInfo();
		}

		return this._instance;
	}

	private constructor() {
		super();
	}

	/**
	 * Returns the internal name of the module.
	 *
	 * @return The internal module name.
	 */
	public override get internalModuleName(): string {
		return 'productionplanning.report';
	}

	/**
	 * Returns the internal pascal case name of the module.
	 *
	 * @return The internal pascal case module name.
	 */
	public override get internalPascalCasedModuleName(): string {
		return 'Productionplanning.Report';
	}

	/**
	 * Loads the translation file used for Report
	 */
	public override get preloadedTranslations(): string[] {
		return [...super.preloadedTranslations, 'cloud.common', 'productionplanning.report', 'productionplanning.mounting', 'model.wdeviewer', 'resource.master', 'basics.country', 'basics.costcodes', 'productionplanning.activity', 'project.costcodes', 'productionplanning.drawing', 'transportplanning.bundle'];
	}

	/**
	 * Returns the entity definitions in the module.
	 *
	 * @return The entity definitions.
	 */
	public override get entities(): EntityInfo[] {
		return [
			PRODUCTIONPLANNING_REPORT_REPORT_ENTITY_INFO,
			PRODUCTIONPLANNING_REPORT_PRODUCT_ENTITY_INFO,
			PRODUCTIONPLANNING_REPORT_DOCUMENT_ENTITY_INFO,
			PRODUCTIONPLANNING_REPORT_DOCUMENT_REVISION_ENTITY_INFO,
			PRODUCTIONPLANNING_REPORT_TIME_SHEET_ENTITY_INFO,
			PRODUCTIONPLANNING_REPORT_COST_CODE_ENTITY_INFO,
			PPS_REPORT_FORM_DATA_ENTITY_INFO,
		];
	}

	/**
	 * @brief Gets the container definitions, including the PDFViewver container configuration.
	 * This method overrides the base class implementation to include a new container definition
	 * @return An array of ContainerDefinition objects including the PDFViewver and Translation container configuration.
	 */
	protected override get containers(): (ContainerDefinition | IContainerDefinition)[] {
		return super.containers.concat([
			DrawingContainerDefinition.createPDFViewer({
				uuid: 'ed673884e4de426eae1c3f18c13ca599',
			}),
			new ContainerDefinition({
				uuid: '5abcc2c46f8c4427a9d743fc5f2f1bd8',
				title: { key: 'cloud.common.entityTranslation' },
				containerType: DataTranslationGridComponent as ContainerTypeRef,
			}),
			new ContainerDefinition({
				containerType: ProductionPlanningReportProductFilterComponent,
				uuid: 'cd94de40c47c4ac9adf6285250e9764f',
				title: { text: 'Product Filter', key: 'productionplanning.report.productFilterTitle' },
				permission: '328d47138f614ae9a020dd616b75b206'
			}),
		]);
	}
}
