/*
 * Copyright(c) RIB Software GmbH
 */

import { LazyInjectableInfo, ILazyInjectionContext, PlatformModuleManagerService } from '@libs/platform/common';
import { Circular1Token, Circular2Token } from '@libs/example/interfaces';


export const LAZY_INJECTABLES: LazyInjectableInfo[] =[
LazyInjectableInfo.create('example.topic-one.Circular1', Circular1Token, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/example/topic-one');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? new importedModule.Circular1(context.injector) : null;
		
	}),

	LazyInjectableInfo.create('example.topic-two.Circular2', Circular2Token, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/example/topic-two');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? new importedModule.Circular2(context.injector) : null;
		
	}),
];
 