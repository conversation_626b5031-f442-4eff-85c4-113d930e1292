/*
 * Copyright(c) RIB Software GmbH
 */

import { IEntityBase } from '@libs/platform/common';

/**
 * Source Entity Generated
 */
export interface ISourceEntityGenerated extends IEntityBase {
	/*
	 * Description
	 */
	Description?: string | null;

	/*
	 * Id
	 */
	Id?: number | null;

	/*
	 * IsDefault
	 */
	IsDefault?: boolean | null;

	/*
	 * ModuleName
	 */
	ModuleName?: string | null;

	/*
	 * Sorting
	 */
	Sorting?: number | null;

	/*
	 * SourceTypeFk
	 */
	SourceTypeFk?: number | null;
}
