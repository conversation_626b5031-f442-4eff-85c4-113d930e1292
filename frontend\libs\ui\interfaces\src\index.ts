export * from './lib/model/ui-circular-one.interface';
export * from './lib/model/ui-circular-two.interface';

/**
 * Returns the module info object for the basics billingschema module.
 *
 * This function implements the {@link IApplicationModule.getModuleInfo} method.
 * Do not remove it.
 * It may be called by generated code.
 *
 * @return The singleton instance of the module info object.
 *
 * @see {@link IApplicationModule.getModuleInfo}
 */
export function getModuleInfo(): IApplicationModuleInfo {
    return BasicsBillingschemaModuleInfo.instance;
}