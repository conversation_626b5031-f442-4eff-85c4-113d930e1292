/*
 * Copyright(c) RIB Software GmbH
 */

import { LazyInjectableInfo } from '@libs/platform/common';

export const LAZY_INJECTABLES: LazyInjectableInfo[] = [
    // Add lazy injectable services here when needed
    // Example for UiBusinessBaseEntityGridService:
    // LazyInjectableInfo.create('ui.business-base.EntityGridService', EntityGridServiceToken, async (context: ILazyInjectionContext) => {
    //     const importedModule = await import('@libs/ui/business-base');
    //     const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
    //     await platformModuleManagerService.initializeModule(importedModule);
    //     return context.doInstantiate ? new importedModule.UiBusinessBaseEntityGridService() : null;
    // }),
];
