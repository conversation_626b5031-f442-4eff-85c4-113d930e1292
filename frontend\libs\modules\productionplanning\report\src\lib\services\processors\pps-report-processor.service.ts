/*
 * Copyright(c) RIB Software GmbH
 */

import { IReportEntity } from '../../model/entities/report-entity.interface';
import { ProductionplanningReportReportDataService } from '../productionplanning-report-report-data.service';
import { IEntityProcessor, IReadOnlyField } from '@libs/platform/data-access';

export class PpsReportProcessorService<T extends IReportEntity> implements IEntityProcessor<T> {
    private dataService!: ProductionplanningReportReportDataService;

    /**
     * Sets the data service instance to avoid circular dependency.
     * @param dataService The ProductionplanningReportReportDataService instance.
     */
    public setDataService(dataService: ProductionplanningReportReportDataService): void {
        this.dataService = dataService;
    }

    /**
     * Processes the given entity and sets read-only fields as required.
     * @param toProcess The IReportEntity to process.
     */
    public process(toProcess: IReportEntity): void {
        if (!toProcess) {
            return;
        }

        //TODO framework does not support to get lookup from  var status = basicsLookupdataLookupDescriptorService.getLookupItem('MntReportStatus', item.RepStatusFk);
        // As a workaround, we treat status values 2 or 3 as "approved" (isApproved = true).
        // TODO: Replace this logic with a proper status lookup when available.
        const status = (toProcess as IReportEntity).RepStatusFk;
        // since adding it manullay treat status 2 or 3 as approved
        const isApproved = status === 2 || status === 3;

        const readonlyFields: IReadOnlyField<IReportEntity>[] = [
            { field: 'Code', readOnly: isApproved },
            { field: 'DescriptionInfo', readOnly: isApproved },
            { field: 'ActivityFk', readOnly: isApproved },
            { field: 'RepStatusFk', readOnly: isApproved },
            { field: 'StartTime', readOnly: isApproved },
            { field: 'EndTime', readOnly: isApproved },
            { field: 'Remarks', readOnly: isApproved },
            { field: 'ClerkFk', readOnly: isApproved },
            { field: 'Userdefined1', readOnly: isApproved },
            { field: 'Userdefined2', readOnly: isApproved },
            { field: 'Userdefined3', readOnly: isApproved },
            { field: 'Userdefined4', readOnly: isApproved },
            { field: 'Userdefined5', readOnly: isApproved },
        ];
        this.dataService.setEntityReadOnlyFields(toProcess, readonlyFields);
    }

    /**
     * Reverts the processing of the given entity.
     * @param toProcess The entity to revert processing on.
     */
    public revertProcess(toProcess: T): void { }
}