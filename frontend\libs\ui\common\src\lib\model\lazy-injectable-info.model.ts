/*
 * Copyright(c) RIB Software GmbH
 */

import { LazyInjectableInfo } from '@libs/platform/common';

export const LAZY_INJECTABLES: LazyInjectableInfo[] = [
    // Add lazy injectable services here when needed
    // Example:
    // LazyInjectableInfo.create('ui.common.SomeService', SomeServiceToken, async (context: ILazyInjectionContext) => {
    //     const importedModule = await import('@libs/ui/common');
    //     const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
    //     await platformModuleManagerService.initializeModule(importedModule);
    //     return context.doInstantiate ? new importedModule.SomeService(context.injector) : null;
    // }),
];
