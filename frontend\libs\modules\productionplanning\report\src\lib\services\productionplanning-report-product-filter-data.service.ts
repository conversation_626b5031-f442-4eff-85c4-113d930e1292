/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';

import { DataServiceFlatRoot, ServiceRole, IDataServiceOptions, IDataServiceEndPointOptions, IDataServiceRoleOptions } from '@libs/platform/data-access';
import { IPpsProductEntityGenerated } from '@libs/productionplanning/shared';
import { Report2ProductComplete } from '../model/models';

@Injectable({
	providedIn: 'root',
})
export class ProductionplanningReportProductFilterDataService extends DataServiceFlatRoot<IPpsProductEntityGenerated, Report2ProductComplete> {
	public constructor() {
		const options: IDataServiceOptions<IPpsProductEntityGenerated> = {
			apiUrl: 'productionplanning/common/product/',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'getproductsbyfilter',
				usePost: true,
			},
			roleInfo: <IDataServiceRoleOptions<IPpsProductEntityGenerated>>{
				role: ServiceRole.Root,
				itemName: 'Products',
			},
			entityActions: { createSupported: false, deleteSupported: false },
		};

		super(options);
	}
}
