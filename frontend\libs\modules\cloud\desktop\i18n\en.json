{"cloud": {"desktop": {"appText": "Home (EN)", "companyFormSubTitle": "Please choose a company and your access role", "companyFormTitle": "Company Selection Dialog", "configurationDisplayName": "Administration", "desktopAdministration": "Administration", "desktopDisplayName": "Desktop", "desktopWorkspace": "Workspace", "favorites": {"addInfo": "Please select your favorite project with the (+-) button", "addProjectDlgHint": "Please select one project from the list", "addProjectDlgTitle": "Add Project to Favorites", "delProjectTitle": "Delete Project from Favorites", "delProjectConfirm": "Would you like to remove project: '{{p1}}' from Favorites?"}, "filterdefConfirmDeleteBody": "Do you really want to delete the filter '{{p1}}'?", "filterdefCriaDelToolTip": "Delete Current Set of Criteria", "filterdefCriaNewToolTip": "Create new set of criteria", "filterdefCrioDelToolTip": "Delete Current Criteria", "filterdefCrioNewToolTip": "Create New Criteria", "filterdefDefaultFilterName": "New Filter", "filterdefFooterBtnDelete": "Delete", "filterdefFooterBtnSave": "Save", "filterdefFooterBtnSearch": "Search", "filterdefInvalidFilter": "Search Declaration Invalid", "filterdefInvalidFilterMsg": "The current filter definition is invalid. Please fix the issues and try again", "filterdefSaveAreaLabel": "Location", "filterdefSaveAreaRole": "Role", "filterdefSaveAreaSystem": "System", "filterdefSaveAreaUser": "User", "filterdefSaveCancelBnt": "Cancel", "filterdefSaveNameLabel": "Filter Name", "filterdefSaveSaveBnt": "Save", "filterdefSaveTitle": "Save Filter: Please enter location and filter name", "formAbout": "About", "formAboutDate": "Version Date: {{p1}}", "formAboutInstallationDate": "Installation Date: {{p1}}", "formAboutVersion": "Version: {{p1}}", "formConfigAllowEnterNavigation": "Enter", "formConfigCancelBnt": "Cancel", "formConfigCloseBnt": "Cancel", "formConfigCustomerLabelName": "User Label Name", "formConfigLabelCode": "Label Code", "formConfigMoveDownBnt": "Move Down", "formConfigMoveUpBnt": "Move Up", "formConfigOKBnt": "OK", "gridConfigDlgHeader": "Grid Layout Dialog", "gridConfigMoveDownBtn": "Move Down", "gridConfigMoveUpBtn": "Move Up", "gridWidthHeader": "<PERSON><PERSON><PERSON>", "groupPrograms": "Programs", "inquiry": {"addallselected": "Add All from Result Set", "addselected": "Add Selected Item", "cancel": "Cancel Selection", "cancelfailed": "Cancel of selection failed. Please try again.", "canceltitle": "Cancel current selection and closes the window", "closeinquirywindowbody": "Items Selection finished. You can now safely close the browser window (tab)", "closeinquirywindowheader": "Items Selection done", "deleteall": "Delete All Items in List", "deleteallquestbody": "Do you want to delete all items already gathered?", "deleteallquestionheader": "Delete All Gathered Items", "deleteitem": "Delete Current Item", "header": "Selection", "saveclose": "Save and Close", "saveclosetitle": "Saves all selected items and closes the window", "savefailed": "Save selection items failed. Please try again.", "datasendpleasewait": "<br><b>The selected data are written into the target fields.<br><br>Please wait.</b>"}, "layoutDialogHeader": "Layout Manager", "loadCompanyDataFailed": "Loading of company list failed. Result was: {{p1}} You will be redirected to Logon", "loadCompanyNoDataBody": "There are no logon companies found for the current user! Please change user and try again. You will be forwarded to the logon dialog", "loadCompanyNoDataTitle": "No companies found for this user", "mainMenuCompany": "Company / Role Selection", "mainMenuCompanyLbl": "Company", "mainMenuLogon": "Logon", "mainMenuLogout": "Logout", "mainMenuRoleLbl": "Role", "mainMenuSettings": "Settings", "moduleDescriptionActivityTemplate": "Management of Activity Templates", "moduleDescriptionActivityTemplateGroup": "Management of activity groups templates", "moduleDescriptionAssetMaster": "Asset Master", "moduleDescriptionBasicsReporting": "Report Configuration", "moduleDescriptionBasicsUnit": "Unit of Measurement", "moduleDescriptionBid": "Management of Bid", "moduleDescriptionBilling": "Sales Invoices", "moduleDescriptionBillingSchema": "Management of Billing Schema", "moduleDescriptionBoqMain": "Bill of Quantities  Main Module", "moduleDescriptionBudgetCode": "Budget Code", "moduleDescriptionBusinessBid": "Management of Bids", "moduleDescriptionBusinessBill": "Management of Bills", "moduleDescriptionBusinessContact": "Contact Management", "moduleDescriptionBusinessOrder": "Management of Orders", "moduleDescriptionBusinessPartner": "Management of Business Partners", "moduleDescriptionBusinessPayment": "Management of Payments", "moduleDescriptionCalendar": "Management of Calendar", "moduleDescriptionCertificate": "Management of Certificate", "moduleDescriptionCharacteristics": "Characteristics", "moduleDescriptionClassification": "Classification", "moduleDescriptionClerk": "Clerk", "moduleDescriptionCloudBoq": "Bill of Quantities (<PERSON><PERSON>)", "moduleDescriptionCloudUnit": "Unit of Measurement", "moduleDescriptionCloudUoM": "Unit of Measurement (Sample)", "moduleDescriptionCompany": "Company Structure", "moduleDescriptionConstructionSystemInstance": "Management of Construction System Instance", "moduleDescriptionConstructionSystemMaster": "Management of Construction System Master", "moduleDescriptionContact": "Management of Company Contacts", "moduleDescriptionContract": "Management of Contracts", "moduleDescriptionControllingUnits": "Controlling Units Structure", "moduleDescriptionCostCodes": "Cost Codes", "moduleDescriptionCostGroup": "Cost Groups", "moduleDescriptionCurrency": "<PERSON><PERSON><PERSON><PERSON>", "moduleDescriptionCustomize": "Client-specific System Customizing", "moduleDescriptionDependentData": "Management of User Container", "moduleDescriptionDocumentFolder": "Document Folder", "moduleDescriptionDocumentImport": "Document Import", "moduleDescriptionEstimate": "Estimate", "moduleDescriptionEstimateAssemblies": "Estimate Assemblies", "moduleDescriptionEvaluationSchema": "Management of Evaluation Schema", "moduleDescriptionExchangeRates": "Exchange Rates", "moduleDescriptionInvoice": "Management of Invoices", "moduleDescriptionMaterial": "Materials", "moduleDescriptionWorkflowMain": "Workflow Module", "moduleDescriptionWorkflowAdmin": "Workflow Admin", "moduleDescriptionMaterialCatalog": "Material Catalogues", "moduleDescriptionModel": "Geometrical Model", "moduleDescriptionNameUsermanagementDescriptor": "Right Descriptor", "moduleDescriptionNameUsermanagementGroup": "Group Management", "moduleDescriptionNameUsermanagementRight": "Right Management", "moduleDescriptionNameUsermanagementUser": "User Management", "moduleDescriptionPackage": "Management of Packages", "moduleDescriptionPayment": "Payment Term Description", "moduleDescriptionPerformanceEntrySheet": "Performance Entry Sheet", "moduleDescriptionPriceComparison": "Management of Price Comparison", "moduleDescriptionPriceCondition": "Management of Price Condition", "moduleDescriptionProcurementConfiguration": "Management of Procurement Configuration", "moduleDescriptionProcurementStructure": "Procurement Structure", "moduleDescriptionProjectMain": "Management of Projects", "moduleDescriptionQTOFormula": "QTO Formula", "moduleDescriptionQuote": "Management of Quotes", "moduleDescriptionRequisition": "Management of Requisition", "moduleDescriptionRfQ": "Management of RfQ", "moduleDescriptionRuleDefinitionMaster": "Management of Rules Definition Master", "moduleDescriptionSalesContract": "Customer Orders", "moduleDescriptionSchedulingMain": "<PERSON><PERSON>ulin<PERSON>, Project Planning", "moduleDescriptionTables": "Tables", "moduleDescriptionText": "Text", "moduleDescriptionTextModules": "Management of Text Modules", "moduleDescriptionTicketSystem": "Management of Ticket System", "moduleDescriptionTranslation": "Translation", "moduleDescriptionUser": "User Management", "moduleDescriptionUserForm": "Management of User Forms", "moduleDescriptionWIC": "Management of Work Item Catalogs", "moduleDescriptionWorkCategories": "Work Categories", "moduleDisplayNameActivityTemplate": "Activity Templates", "moduleDisplayNameActivityTemplateGroup": "Activity Groups Templates", "moduleDisplayNameAssetMaster": "Asset Master", "moduleDisplayNameBank": "Bank", "moduleDisplayNameBasicsConfig": "<PERSON><PERSON><PERSON>", "moduleDisplayNameBasicsReporting": "Reporting", "moduleDisplayNameBasicsUnit": "Unit", "moduleDisplayNameBilling": "Billing", "moduleDisplayNameBillingSchema": "Billing <PERSON><PERSON>", "moduleDisplayNameBoqMain": "BoQ", "moduleDisplayNameBudgetCode": "Budget Code", "moduleDisplayNameBusinessBid": "Bids", "moduleDisplayNameBusinessBill": "Bills", "moduleDisplayNameBusinessContact": "Contacts", "moduleDisplayNameBusinessPartner": "Business Partner", "moduleDisplayNameBusinessPayment": "Payment", "moduleDisplayNameBusinessWIC": "WIC", "moduleDisplayNameCalendar": "Calendar", "moduleDisplayNameCertificate": "Certificate", "moduleDisplayNameCharacteristics": "Characteristics", "moduleDisplayNameClassification": "Classification", "moduleDisplayNameClerk": "Clerk", "moduleDisplayNameCloudBoq": "Boq (sample)", "moduleDisplayNameCloudUnit": "Unit", "moduleDisplayNameCompany": "Company", "moduleDisplayNameConstructionSystemMaster": "Construction System Master", "moduleDisplayNameContact": "Contacts", "moduleDisplayNameContract": "Contract", "moduleDisplayNameControllingUnits": "Controlling Units", "moduleDisplayNameCostCodes": "Cost Codes", "moduleDisplayNameCostGroup": "Cost Groups", "moduleDisplayNameCountry": "Country", "moduleDisplayNameCurrency": "<PERSON><PERSON><PERSON><PERSON>", "moduleDisplayNameCustomize": "Customizing", "moduleDisplayNameDocumentImport": "Document Import", "moduleDisplayNameEstimate": "Estimate", "moduleDisplayNameEstimateAssemblies": "Assemblies", "moduleDisplayNameInvoice": "Invoice", "moduleDisplayNameMaterial": "Material", "moduleDisplayNameExampleOne": "Example 1", "moduleDisplayNameWorkflowMain": "Workflow Main", "moduleDisplayNameWorkflowAdmin": "Workflow Admin", "moduleDisplayNameMaterialCatalog": "Material Catalog", "moduleDisplayNameModel": "Model", "moduleDisplayNamePackage": "Package", "moduleDisplayNamePayment": "Payment Term", "moduleDisplayNamePerformanceEntrySheet": "PES", "moduleDisplayNamePriceComparison": "Price Comparison", "moduleDisplayNamePriceCondition": "Price Condition", "moduleDisplayNameProcurementConfiguration": "Procurement Configuration", "moduleDisplayNameProcurementStructure": "Procurement Structure", "moduleDisplayNameProjectMain": "Project", "moduleDisplayNameQTO": "QTO", "moduleDisplayNameQTOFormula": "QTO Formula", "moduleDisplayNameQuote": "Quote", "moduleDisplayNameRequisition": "Requisition", "moduleDisplayNameRfQ": "RfQ", "moduleDisplayNameSchedulingMain": "Scheduling", "moduleDisplayNameText": "Text", "moduleDisplayNameTextModules": "Text Modules", "moduleDisplayNameTicketSystem": "Ticket System", "moduleDisplayNameTranslation": "Translation", "moduleDisplayNameUser": "User", "moduleDisplayNameUsermanagementGroup": "Groups", "moduleDisplayNameUsermanagementRight": "Roles", "moduleDisplayNameUsermanagementUser": "Users", "moduleDisplayNameWIC": "WIC", "moduleDisplayNameWorkflowAdministration": "Workflow Administration", "navBarExportDesc": "Export", "navBarExportLayouts": "Export All Views", "navBarGoToFirstDesc": "Go to First", "navBarGoToLastDesc": "Go to Last", "navBarGoToNextDesc": "Go to Next", "navBarGoToPrevDesc": "Go to Previous", "navBarImportDesc": "Import", "navBarModuleDocuDesc": "Module Documentation", "productName": "iTWO 5D Cloud", "reloadApplicationHint": "Remark: Saving the company or role changes will force an application reload", "sbdeletePinningContext": "Remove pinned item from Context", "sbPinningContextnotAvailable": "No Item Found", "sdCmdBarFavorites": "Favorites", "sdCmdBarInfo": "Info", "sdCmdBarSearch": "Search", "sdCmdBarWatchlist": "Watchlist", "sdCmdBarWorkflow": "Workflow", "sdGoogleBelongtoBPChk": "Belonging to this Company", "sdGoogleExecHintsChk": "Enable Execution Info", "sdGoogleInactiveChk": "Include inactive items", "sdGoogleNoSearchResult": "No search results", "sdGoogleSearchFilter": "Insert Filter", "sdInformationTitle": "Information", "sdMainFavoritesTitle": "Project Favorites", "sdMainSearchBtnEnhanced": "Enhanced Search", "sdMainSearchBtnSettings": "Settings", "sdMainSearchTitle": "Search", "sdSearchFailedInfo": "Filter Request failed", "sdSearchOptionLabel": "Search Options", "sdSearchRunning": "Search Running", "sdSettingsExecHintLabel": "Execution Info of last filter call", "sdSettingsHeader": "Settings", "sdSettingsPageInfoLabel": "Records per Page", "sdSettingSupportLabel": "Help Desk Service Parameter", "sdWizardTitle": "Wizards", "sdWizzardsAccordionHeader1": "GAEB", "sdWizzardsAccordionHeader1Sub1": "GAEB-Import", "sdWizzardsAccordionHeader1Sub2": "GAEB-Export", "sdWizzardsAccordionHeader2": "BoQ", "sdWizzardsAccordionHeader2Sub1": "<PERSON><PERSON><PERSON>", "settingsForceLagout": "Force Logout", "settingsFormTitle": "Settings", "settingsHeader": "User Settings", "settingsLanguageEnglish": "English", "settingsLanguageGerman": "De<PERSON>ch", "settingsUserDataLang": "User Data Language", "startpage": "Home", "tileGroupConfiguration": "Configuration", "tileGroupMasterData": "Master Data", "tileGroupMasterDataSecond": "Master Data 2", "tileGroupProcurement": "Procurement", "tileGroupProject": "Project", "tileGroupSales": "Sales", "tileGroupUsermanagement": "User Management", "watchlist": {"adddlglblarea": "Area", "adddlglblname": "Name", "adddlgtitle": "Add Elements ({{p1}}) to Watchlist", "addtowatchlisttp": "Add to Watchlist", "backtomaintp": "Back to Watchlist View", "ctxmenudefaultlist": "Set as <PERSON><PERSON><PERSON>", "ctxmenudelete": "Delete", "ctxmenuedit": "Details", "deletedefaultlist": "Clear default Watchlist", "deletewatchlistbody": "Do you really want to delete the watchlist?", "deletewatchlistelembody": "Do you really want to delete the watchlist element?", "deletewatchlistelemtitle": "Delete Watchlist Element", "deletewatchlistitem": "Delete Watchlist Entry", "deletewatchlisttitle": "Delete Watchlist", "editwatchlistnameph": "Enter Watchlist Name", "errnotallowedbody": "You are not allowed to perform this operation", "errnotallowedtitle": "Disallowed Operation", "infoalltitle": "Info Message Watchlist", "infoalreadyinlist": "Element is already part of watchlist", "infonoitembody": "There are no watchlist elements in the list. Navigation is not possible", "infonoitemtitle": "No Watchlist Elements", "nowatchlistcontent": "There are no watchlists available", "refresh": "Refresh Watchlists", "refreshitems": "Refresh Items", "summarymissing": "N/A: Summary Missing", "title": "Watchlists", "titlecompanywl": "Company", "titledetails": "Details", "titlerolewl": "Role", "titleuserwl": "User", "infoFewInList": "Some elements are already part of watchlist. New elements are added", "titlelist": "Watchlists"}, "moduleDescriptionChangeMain": "Management of Changes", "moduleDisplayNameChangeMain": "Changes", "formConfigLoading": "Loading. Please wait", "design": {"autoCalc": "Automatic Calculation", "backgroundImage": "Background Image", "brandingSystem": "UI Branding (System)", "brandingUser": "UI Branding (User)", "config": "General Settings", "errors": {"defaultMessage": "The file {{ name }} could not be uploaded because of an unknown error", "defaultTitle": "Action Canceled", "invalidTitle": "Invalid selection", "maxSizeMessage": "The file {{ name }} exceeds the allowed file size of {{ size }} and cannot be used", "actionUnsuccessful": "Action Unsuccessful", "notAllowedMessage": "The file type '{{ type }}' is not allowed.", "unsavedSettings": "The following settings could not be saved:"}, "iconColor": "Icon Color", "logo1": "Logo 1", "logo2": "Logo 2", "mainColor": "Main Color", "mouseHoverColor": "<PERSON> Hover", "personal": "Personalization", "selectedColor": "Selected", "separatorColor": "Separator Color", "sidebarPos": "Sidebar Position", "textColor": "Text Color", "headerColor": "Header Color", "headerTextColor": "Text Color of Header", "deleteBtnTitle": "Delete Image", "left": "Left", "resetToDefault": "Reset to <PERSON><PERSON><PERSON>s", "brandingPortal": "UI Branding", "desktop": {"groupGroups": "Desktop Groups", "groupName": "Group Name", "groupPages": "Desktop Pages", "groupTiles": "Desktop Tiles", "layoutSystem": "Desktop Layout", "layoutUser": "Desktop Layout", "pageName": "Page Name", "tileColor": "Tile Color", "tileOpacity": "Tile Opacity", "tileSize": "<PERSON><PERSON>", "tileConfig": "Tile Config", "tileProperties": "Tile Properties", "tileSize0": "Small", "tileSize1": "Big", "groupHome": "Homepage", "layoutPortal": "Desktop Layout", "addWebTile": "Add New Web Tile", "type": "Type", "typExternal": "External", "typInternal": "<PERSON><PERSON><PERSON>", "webTileDescription": "Description", "webTileIcon": "Icon", "webTileName": "Name", "webTilesCreate": "Create Web Tile", "webTilesDelete": "Delete Web Tile", "webTileUrl": "URL", "typWeb": "Web", "customTileCreate": "Create Custom Tile", "customTileDelete": "Delete Custom Tile", "moduleTile": "<PERSON><PERSON><PERSON>", "typQuickstart": "Quick Start", "typPinnedDocuments": "Pinned"}, "activation": "Activation", "quickstart": {"infoMessage": "All modules are displayed, therefore no selection is necessary.", "useSettings": "Activate User Settings", "showPages": "Show Pages", "showTabs": "Show Module Tabs", "moduleList": "Selected Modules"}, "quickstartGridColumn": "<PERSON><PERSON><PERSON>", "quickstartModuleList": "QuickStart - Module List", "quickstartUser": "Quick Start", "loadingBarColor1": "Loading Bar Color 1", "loadingBarColor2": "Loading Bar Color 2", "logo3": "<PERSON><PERSON>", "gridLayout": {"gridLayoutPortal": "Grid Layout", "gridLayoutSystem": "Grid Layout", "gridLayoutUser": "Grid Layout", "readOnlyGroupTitle": "Display of Read Only Cells", "roColor": "Colour", "roPreview": "Preview", "roShowColor": "Show Color", "roShowSymbol": "Show Symbol"}, "dataLayout": {"dataLayoutSystem": "Data Layout", "dataLayoutUser": "Data Layout", "displayReadOnlyFields": "Display of Read Only fields", "groupFormular": "Formular", "groupGeneral": "General", "groupGrid": "Grid", "roBackgroundColor": "Read Only Background Color", "roPreview": "Preview", "roShowColorSymbol": "Show Color and Symbol", "roShowNothing": "Deactivated", "roSymbolColor": "Read Only Symbol Color", "dataLayoutPortal": "Data Layout", "roShowColor": "Show Color", "roShowSymbol": "Show Symbol", "roShowStripes": "Show Stripes", "backgroundColor": "Selection background colour", "borderColor": "Selection border colour", "colorScheme": "Color Scheme", "gridBackgroundColour": "Background Colour", "gridFontColour": "Font Colour", "gridFontSize": "Font Size", "gridFontWeight": "Font Weight", "gridLevel": "Level", "groupDataSelection": "Data Selection", "groupHierarchical": "Hierarchical Grid", "groupReadonly": "Read Only Fields", "groupTheme": "Theme", "hierarchicalDefaultDesc": "The settings apply to all levels for which there are no level-specific settings.", "hierarchicalDefaultTitle": "<PERSON><PERSON><PERSON>", "hierarchicalSpecificDesc": "Different settings can be defined for each level of the hierarchical table. The level-specific settings overwrite the default settings.", "hierarchicalSpecificTitle": "Level specific settings", "themeBlue": "Blue", "themeCustom": "Custom", "themeGreen": "Green", "themeOrange": "Orange", "themeTeal": "<PERSON><PERSON>", "groupDescriptionDataSelection": "The colour of the selection border and the background colour of the selected elements can be set here.", "groupDescriptionReadonly": "The background colour of the cell for read-only fields and the colour of the symbol that can optionally be displayed for read-only fields can be defined here."}, "alertMessages": {"amSystem": "Alert Messages", "clear": "Reset Message", "groupShutdownMessage": "Shutdown Message", "isActive": "active", "shutdownMessage": "Message", "validFrom": "<PERSON><PERSON>", "validUntil": "<PERSON>id <PERSON>", "forceLogoff": "Force Logoff", "info": "<u>Template Placeholder Info</u><div>for <b>Valid Until</b> use: <b><%=until%></b>'<div>for <b>Minutes Until</b> use: <b><%=minutestogo%></b></div>", "preview": "Preview", "certificate": {"friendlyName": "Friendly Name", "idSrvTitle": "<b>Identity Server Certificate</b>", "isValid": "Is <PERSON>", "thumbPrint": "Thumbprint", "validDays": "Days Valid", "validUntil": "<PERSON>id <PERSON>", "webSrvTitle": "<b>Web Server SSL Certificate</b>", "idSrvSslTitle": "<b>Identity Server SSL Certificate</b>"}, "groupCertificateMessage": "Certificate Message(s)"}, "right": "Right", "useSettings": "Activate Settings", "quickstartSystem": "Quick Start", "iconColorHovered": "Icon Color Hovered", "iconColorSelected": "Icon Color Selected", "headerTextColorTooltip": "Color of the text in the RIB 4.0 header. This value changes the color of the text for the module name, the selected record, and the login information.", "iconColorHoveredTooltip": "Color of the Sidebar icon when the mouse hovers over the button", "iconColorSelectedTooltip": "Color of the sidebar icon when the button is selected", "iconColorTooltip": "Color of the sidebar icon", "loadingBarColor1Tooltip": "Color 1 of loading bar in RIB 4.0 header. \nTwo color values can be defined. When the color values are the same, a continuous bar is displayed, when the values are different, a dashed bar is displayed.", "loadingBarColor2Tooltip": "Color 2 of loading bar in RIB 4.0 header.", "mainColorTooltip": "Sidebar and dialog header color", "mouseHoverColorTooltip": "Color of the button in the side bar when hovering with the mouse", "selectedColorTooltip": "Color of the selected button in the sidebar", "textColorTooltip": "Color of text displayed in the header of dialogs", "feedbackMessages": {"long": "This request could take <b>several minutes</b> due to the number of items bring processed.<br><br>We will notify you once complete.", "medium": "This request could take <b>60 seconds or longer</b> due to the number of items bring processed.<br><br>We will notify you once complete.", "short": "This request could take <b>up to one minute</b> due to the number of items bring processed.<br><br>We will notify you once complete.", "xshort": "This request will take a couple of seconds"}}, "mainMenuDesign": "Design", "moduleDescriptionObjectMain": "Object Units", "moduleDescriptionProductionPlanning": "Manage of Production Units", "moduleDisplayNameObjectMain": "Object Units", "tileGroupProductionPlanning": "Production Planning", "moduleDescriptionModelChange": "Difference Between Models", "moduleDescriptionModelChangeSet": "Comparison Between Models", "moduleDescriptionSite": "Site", "moduleDisplayNameModelChangeSet": "Model Comparison", "moduleDisplayNameSite": "Site", "moduleDescriptionResourceMaster": "Resource Master", "moduleDisplayNameResourceMaster": "Resource Master", "moduleDisplayNameResourceReservation": "Resource Reservation", "sdCmdBarTask": "Tasklist", "moduleDisplayNameForContact": "Contact", "moduleDisplayNamePpsMaterial": "PPS Material Configuration", "moduleDisplayNameDefect": "Defect", "moduleDisplayNamePPSHeader": "PPS Header", "moduleDisplayNameProductionSet": "Production Set", "moduleDisplayNameiTWOcity": "iTWO City", "moduleDescriptionBundle": "Management of Bundles", "moduleDescriptionEquipment": "Management of Plants", "moduleDescriptionNameResourceReservation": "Resource Reservation", "moduleDescriptionPpsMounting": "Management of Installation", "moduleDescriptionResourceRequisition": "Resource Requisition", "moduleDescriptionResourceTypes": "Resource Types", "moduleDescriptionTransport": "Management of Transport", "moduleDisplayNameBundle": "Bundle", "moduleDisplayNameExternalSystemConfiguration": "External System Configuration", "moduleDisplayNameResourceRequisition": "Resource Requisition", "moduleDisplayNameResourceTypes": "Resource Types", "moduleDisplayNameResourceEquipmentGroup": "Resource Equipment Group", "moduleDisplayNameStock": "Stock", "moduleDisplayNameTransport": "Transport", "moduleDisplayNameTrsPackage": "Transport Package", "tileGroupTransportPlanning": "Transport Planning", "moduleDescriptionEquipmentGroup": "Plant Group", "moduleDescriptionTransportRequisition": "Management of Transport Requisitions", "moduleDisplayNameTransportRequisition": "Transport Requisitions", "formAboutBuildNo": "Buildnumber: {{p1}}", "moduleDisplayNameLog": "Log", "moduleDescriptionEquipmentCatalogs": "Management of Plant Catalogs", "moduleDescriptionLogisticJob": "Management of Logistic Jobs", "moduleDescriptionNameScheduler": "Scheduler Management", "moduleDescriptionPpsConfiguration": "Management of PPS Configuration", "moduleDisplayNameLogisticJob": "Logistic Job", "moduleDisplayNamePpsConfiguration": "PPS Configuration", "moduleDisplayNamePpsFormworkType": "Formwork Type", "moduleDescriptionPpsFormworkType": "Management of Formwork Type", "moduleDescriptionPlantComponentType": "Administration Plant Component Type", "moduleDisplayNamePlantComponentType": "Plant Component Type", "moduleDescriptionModelAdministration": "Application-wide Model Settings", "moduleDescriptionModelEvaluationMaster": "Administration of Evaluation Rules for Models", "moduleDisplayNameModelAdministration": "Model Administration", "moduleDisplayNameModelEvaluationMaster": "Model Evaluation Master", "moduleDisplayNameResourceResquisition": "Resource Requisition", "moduleDisplayNameResourcesTypes": "Resource Types", "moduleDisplayNameTxActivityStream": "iTWOtx Activity Stream", "module": "<PERSON><PERSON><PERSON>", "moduleDescriptionPpsReport": "Management of Installation Report", "moduleDisplayNamePpsReport": "Installation Report", "settings": {"language": "Language", "languageSettings": "Language Settings", "portalTitle": "Portal Settings", "systemTitle": "System Settings", "availableModules": "Available Modules", "selectedModules": "Selected Modules", "userTitle": "User Settings"}, "sorting": "Sorting", "visibility": "Visibility", "moduleDescriptionNameLogisticDispatching": "Dispatching Notes", "moduleDisplayNameLogisticDispatching": "Dispatching Notes", "moduleDescriptionPpsActivity": "Management of Installation Activity", "moduleDisplayNamePpsStage": "Mounting Stage", "modules": "<PERSON><PERSON><PERSON>", "moduleDisplayNameLogisticPriceCondition": "Logistic Price Condition", "moduleDisplayNameLogisticSundryService": "Sundry Service", "moduleDescriptionResourceEnterprise": "Enterprise Resource Planning", "moduleDescriptionResourceProject": "Project Resource Planning", "moduleDisplayNameLogisticSettlement": "Settlement", "moduleDisplayNameResourceEnterprise": "Enterprise Resources", "moduleDisplayNameResourceProject": "Project Resources", "moduleDisplayNameLogisticSundryServiceGroup": "Sundry Service Group", "moduleDescriptionControllingCostCodes": "Controlling Cost Codes", "moduleDescriptionEngineering": "Management of Engineering Planning", "moduleDisplayNameControllingCostCodes": "Controlling Cost Codes", "moduleDisplayNameEngineering": "Engineering Planning", "mainMenuDataLanguageLbl": "Data Language", "mainMenuUiDataSubTitle": "User specific User Interface and Data Languages settings.", "mainMenuUiLanguageLbl": "UI Language", "permission": {"objectPermission": "Object"}, "moduleDisplayNameTimekeepingEmployee": "Employee", "moduleDisplayNameTimekeepingShiftModel": "Shift Model", "moduleDescriptionYTWOCustomerPurchaseOrder": "Management of Customer Purchase Order", "moduleDescriptionYTWODeliveryNotice": "Management of Delivery Notice", "moduleDescriptionYTWOInvoiceNotice": "Management of Invoice Notice", "moduleDisplayNameYTWOCustomerPurchaseOrder": "Customer Purchase Order", "tileGroupYTWO": "YTWO", "moduleDescriptionTimekeepingTimeSymbols": "Timekeeping Time Symbols", "moduleDisplayNameTimekeepingTimeSymbols": "Time Symbols", "sdCmdBarSkypeForBusiness": "Skype for Business", "skype": {"chat": "Cha<PERSON>", "closeChatPopup": "Close all Chat Windows", "myContacts": "My Contacts", "searchContacts": "Search Result", "suggestContacts": "Suggest Contacts", "title": "Skype", "callHome": "Call Home", "callMobile": "Call Mobile", "callWork": "Call Work", "company": "Company", "im": "IM", "office": "Office", "otherPhone": "Other Phone", "sendMail": "Send Email", "searchPlaceHolder": "Find someone", "itemCount": "{{count}} Items", "audio": "Audio", "contactCard": "Contact Card", "favorite": "Favorite", "video": "Video", "navigation": "Navigation", "sendLeadingItems": "Send Selected Leading Items", "favoriteContracts": "Favorite Contacts", "sdk": {"loadFailed": "Skype SDK Load Failed."}, "startFromOutSide": {"clerkHasNoEmail": "Clerk has no email.", "loginRequired": "Please login your skype account.", "openSidebarFailed": "Open Sidebar Failed.", "startFailed": "Start skype chat failed.", "start": "Start skype chat"}, "login": "<PERSON><PERSON>"}, "navBarModuleVideoDesc": "Module Video", "oneDrive": {"button": {"replace": "Replace the files", "skip": "Skip these files"}, "conflictMessage": "The destination has {{count}} file(s) with the same name", "login": "<PERSON><PERSON>", "rootFolderName": "Root", "toolBar": {"copy": "Copy", "delete": "Delete", "newFile": "New File", "newFolder": "New Folder", "paste": "Paste", "properties": "Attributes", "rename": "<PERSON><PERSON>"}, "grid": {"name": "Name", "size": "Size", "lastModifiedDateTime": "Date modified"}, "loginRequired": "Please login your office365 account.", "title": "OneDrive"}, "sdCmdBarOneDrive": "OneDrive", "moduleDisplayNameDashboardConfiguration": "Dashboard Configuration", "moduleDisplayNameRegionCatalog": "Region Catalog", "moduleDescriptionNameControlTower": "Powered by PowerBI", "moduleDescriptionNameControlTowerConfiguration": "Control Tower Configuration", "moduleDisplayNameControlTower": "Control Tower 4.0", "moduleDisplayNameControlTowerConfiguration": "Control Tower 4.0 Configuration", "botChat": {"help": "Help", "openURL": "Open URL", "saysome": "Enter your message...", "send": "Send", "chatHistory": "Chat History", "next": "Next", "previous": "Previous", "tapToEdit": "Tap to edit", "showIt": "Show it!", "linkTo": "See details", "dateError": "Date format error", "emailError": "Email format error", "empty": "can not be empty", "identityCardError": "Identity card format error", "phoneError": "Phone number format error", "timeError": "Time format error", "urlError": "Url format error", "afternoon": "", "AM": "AM", "evening": "", "friday": "Friday", "monday": "Monday", "morning": "", "PM": "PM", "saturday": "Saturday", "sunday": "Sunday", "thursday": "Thursday", "today": "Today", "tuesday": "Tuesday", "wednesday": "Wednesday", "FAQ": "FAQ's", "cancel": "Cancel", "no": "No", "ok": "OK", "ok1": "OK", "quit": "quit", "sure": "sure", "yes": "Yes", "fromMoreThanTo": "The from time cannot be greater than to time", "Content": "Content", "Description": "Description", "EmailAddress": "Email address", "from": "From", "personName": "Name", "schedule_date": "Meeting date", "Subject": "Subject", "to": "To", "fromEqualTo": "The \"from\" time cannot equal the \"to\" time.", "yesterday": "Yesterday", "Attendees": "Email address", "Name": "Name", "maxLength": "Input length cannot exceed {{maxLength}}", "updateProgress": "update progress for {{p1}}", "quitKeyword": "cancel, quit", "taskCanceled": "The task has been terminated.", "someError": "We experienced a technical error, please contact your admin.", "skip": "<PERSON><PERSON>"}, "taskList": {"header": "ToDo", "headerTask": "Tasks", "mainEntityFilter": "Filter by <PERSON> En<PERSON>", "clear": "Clear Noticification", "groupOrSortingSetting": "Group & Sorting Setting", "groupOrSortingSettingHeaderText": "Save setting: Please enter location and setting name.", "groupOrSortingSettingnameLabelText": "Setting name", "groupOrSortingSettingareaLabelText": "setting name", "newSetting": "New Setting", "settingdefConfirmDeleteTitle": "Delete Setting", "settingdefConfirmDeleteBody": "Do you really want to delete the setting '{{p1}}'?", "refresh": "Refresh", "sortAscending": "Sort ascending", "sortDescending": "Sort descending", "maximize": "Maximize", "grouping": {"noGrouping": "No Grouping", "clerk": "Grouped by Clerk", "prio": "Group by Priority", "lifeTime": "Group by Lifetime", "entity": "Entity Description", "template": "Template", "endDate": "End Date", "description": "Group by Description", "noEndDate": "No End Date", "ended": "ended", "MyTasks": "My Tasks", "userDefined1": "User Defined 1", "userDefined2": "User Defined 2", "userDefined3": "User Defined 3", "userDefined4": "User Defined 4", "userDefined5": "User Defined 5", "userDefinedMoney1": "User Defined Money 1", "userDefinedMoney2": "User Defined Money 2", "userDefinedMoney3": "User Defined Money 3", "userDefinedMoney4": "User Defined Money 4", "userDefinedMoney5": "User Defined Money 5", "userDefinedDate1": "User Defined Date 1", "userDefinedDate2": "User Defined Date 2", "userDefinedDate3": "User Defined Date 3", "userDefinedDate4": "User Defined Date 4", "userDefinedDate5": "User Defined Date 5", "startDate": "start Date", "status": "Status", "action": "Action", "type": "Type"}, "sorting": {"noSorting": "Default Sorting", "start": "By Start", "prio": "By Priority", "endTime": "By End Time", "userDefined1": "By User Defined 1", "userDefined2": "By User Defined 2", "userDefined3": "By User Defined 3", "userDefined4": "By User Defined 4", "userDefined5": "By User Defined 5", "userDefinedMoney1": "By User Defined Money 1", "userDefinedMoney2": "By User Defined Money 2", "userDefinedMoney3": "By User Defined Money 3", "userDefinedMoney4": "By User Defined Money 4", "userDefinedMoney5": "By User Defined Money 5", "userDefinedDate1": "By User Defined Date 1", "userDefinedDate2": "By User Defined Date 2", "userDefinedDate3": "By User Defined Date 3", "userDefinedDate4": "By User Defined Date 4", "userDefinedDate5": "By User Defined Date 5", "startDate": {"asc": "By start date (Asc)", "desc": "By start date (Desc)"}, "status": {"asc": "Status (Asc)", "desc": "Status (Desc)"}}, "showFilter": "Filter Tasks", "executefilter": "Execute filter", "clearfilter": "Clear filter", "filter": "filter", "back": "Back", "settings": "Settings", "pin": "<PERSON>n", "startWorkflow": "Start Workflow", "startWorkflowEveryEntity": "Start Workflow for every Entity", "startWorkflowSelectedEntities": "Start one Workflow for all selected Entities", "newPin": "New Pin", "loadingInfo": "Start workflow: '{{p_0}}'", "workflowDescription": "Description: {{p_0}}", "workflowStatus": "Status: {{p_0}}", "workflowError": "Error: {{p_0}}", "workflowHistory": "History:"}, "taskDetail": {"userTasks": "User Tasks", "allTasks": "All", "userTaskDetail": "User Task Details", "userTaskCaption": "user task caption"}, "moduleDisplayNameResourceSkill": "Resource Skills", "moduleDescriptionAccountingJournals": "Accounting Journals", "moduleDisplayNameAccountingJournals": "Accounting Journals", "moduleDescriptionNameWorkflowTask": "Task Module", "moduleDisplayNameWorkflowTask": "Task", "searchEnhanced": {"dropdownCreate": "Create search form...", "onlyShowActive": "Hide inactive elements in selection lists", "tooltip": "A enhanced search is active. The search criteria of the following filter must be matched: {{btnRefreshTooltip}}", "maintitle": "Enhanced Search"}, "searchform": {"dropdownItemDelete": "Delete the current form...", "dropdownItemEdit": "Edit the current form...", "maintitle": "Search Forms", "searchformSearchPlaceholder": "Search something...", "default": "The search term is a default value:", "literal": "Literal", "variable": "Variables", "tooltip": "A search form is active. The search criteria of the following search form must be matched: {{btnRefreshTooltip}}", "tooltipWithoutSearch": "A search form is active. No search form available", "tooltipWithSearch": "A search form is active. The search criteria of the following search form must be matched: {{btnRefreshTooltip}}"}, "searchFormWizard": {"headerTitle": "Create Search Form", "step1": {"titleSearchCriteria": "Search Criteria", "titleSearchQuery": "Search Query", "topDescription": "Choose the search criteria that can be edited in the search form. Criteria that hasn't been chosen will be considered during the search, but cannot be influenced by the user.", "subHeaderTitle": "Step 1"}, "step2": {"descriptionNoSearchCriteria": "No search criteria has been chosen <br> The following query will be executed without entered criteria in the search form:", "labelCriteria": "Label:", "labelOperator": "Operator:", "labelSearchterm": "Search Term:", "leftSubTitle": "Visibility of the search criteria", "placeholderCriteria": "Enter label here", "rightSubTitle": "Preview", "title": "Display Options", "topDescription": "Set the appearance of the search form for the user.", "subHeaderTitle": "Step 2", "variableRangeExpr": {"day": "Day", "days": "Days", "descriptionVariableRangeTemplate": "From the present time ${0} ${1} before and ${2} ${3} after", "hour": "Hour", "hours": "Hours", "month": "Month", "months": "Months", "week": "Week", "weeks": "Weeks", "year": "Year", "years": "Years"}, "rightSubTitleDescription": "Select the checkboxes to activate/inactivate in the search form"}, "step3": {"labelAvailableSearchForms": "Available Search Forms", "labelDescription": "Description", "labelLocation": "Location", "labelSearchFormName": "Search Form Name", "labelShortName": "Short Name", "topDescription": "Set the properties of the search form.", "placeholderlabel": "No data available", "subHeaderTitle": "Step 3"}, "step4": {"defaultValue": "Default Value", "dropdownLabel": "Input field", "inlineLabel": "Inline", "literalDisplayLabel": "Set literal display as:", "multiSelectLabel": "MultiSelect", "variableDisplayLabel": "Set variable display as:", "customSequnceTitle": "Display Order", "subHeaderTitle": "Step 4", "topDescription": "Use the mouse to drag the items into the correct order."}}, "searchGoogle": {"maintitle": "Standard Search", "calendar": "Calendar", "variable": "Variable Period", "tooltipWithoutSearch": "The standard search is active. No search term is defined. All data in database are loaded.", "tooltipWithSearch": "The standard search is active. Only data corresponding to the current search criterion {{btnRefreshTooltip}} is loaded"}, "moduleDescriptionResourceMaintenance": "Plant Maintenance Schemes", "moduleDisplayNameLogisticCard": "Job Cards", "moduleDisplayNameLogisticCardTemplate": "Job Card Templates", "clerkProxy": {"absence": "Absence", "absenceFrom": "Absent From", "absenceStepTitle": "Set Absence", "absenceTo": "Absent To", "dialogTitle": "Clerk Absence Proxy", "optionLastProxy": "Use same Absence Proxy Rules as for last Absence Period", "optionStandard": "Take Standard Proxy defined on Clerk", "proxyType": "Proxy Type", "reason": "Absence Reason", "creationError": "Error when creating Absence", "creationSuccess": "Absence created successfully", "absenceListStepTitle": "Recorded Absences", "createAbsence": "Create new Absence", "createProxy": "Create new Proxy", "currentProxy": "Current Default Proxy for your Absences:", "deleteAbsence": "Delete Absence", "deleteProxy": "Delete Proxy", "errorCreateAbsence": "Absence could not be created", "errorDeleteAbsence": "Please select an Absence first", "errorDeleteProxy": "Please select a Proxy first.", "lastConfig": "When no Proxy is specified, the last Proxy Configuration will be taken", "loadingData": "Loading Data...", "noClerkFound": "No associated Clerk found for your User", "noProxy": "no Default Proxy", "proxyForTimeSpan": "Here you can configure Proxies for your Absence {{description}} - From: {{from}} To: {{to}} ", "proxyListStepTitle": "Clerk Proxy for selected Absence"}, "autoFilter": {"delete": "Remove Auto Filter", "pause": "Pause Auto Filter", "save": "Save current search options as Auto Filter", "saveMsg": "The following search options are set as Auto Filters. The Auto Filter is executed when the module is loaded. <br> <br> {{option1}}{{option2}}{{option3}}{{option4}}{{option5}}", "title": "Auto Filter", "unpause": "Resume Auto Filter"}, "sdGoogleDateSearchChk": "Search by Date", "sdGoogleRestoreEntitiesChk": "Restore previously loaded entities", "moduleDescriptionEngineeringDrawing": "Management of Engineering Drawing", "moduleDisplayNameEngineeringDrawing": "Engineering Drawing", "moduleDisplayNameWorkOperationTypes": "Work Operation Types", "moduleDisplayNamePpsAccounting": "Engineering Accounting", "moduleDisplayNameModelMap": "Map", "moduleDescriptionModelMap": "Level-Based Plans of Models", "moduleDescriptionProductTemplates": "Management of Product Templates", "moduleDescriptionProductTemplate": "Management of Product Template", "moduleDescriptionTimekeepingRecording": "Recording of Working Hours", "moduleDisplayNameTimekeepingRecording": "Timekeeping", "moduleDescriptionTimekeepingPeriod": "Managing Timekeeping Periods", "moduleDisplayNameTimekeepingPeriod": "Timekeeping Period", "moduleDescriptionTimekeepingLayout": "Timekeeping Input Form Layout", "moduleDisplayNamePpsEventConfig": "PPS Event Configuration", "moduleDisplayNameTimekeepingLayout": "Timekeeping Layout", "moduleDescriptionPlantCertificate": "Management of Plant Certificates", "moduleDisplayNamePlantCertificate": "Plant Certificate", "moduleDescriptionControllingActuals": "Record and maintain actual costs", "moduleDescriptionInventory": "Management of Inventory", "moduleDescriptionPPSProduct": "Management of Product", "moduleDisplayNamePPSProduct": "Product", "moduleDescriptionBasicsRiskEvents": "Create Risk Events", "moduleDisplayNameBasicsRiskEvents": "Risk Events", "moduleDescriptionBasicsRiskRegister": "Create Risk Registers", "moduleDisplayNameCloudUitesting": "Cloud UITesting", "aboutdialog": {"softwareLicencesText": "Licences", "certificatesText": "Certificates", "descriptionMobileSolution": "With the iTWO Mobile Solutions we help you to start your enterprise mobility journey by supporting different processes with real-time integrated apps to iTWO 4.0. Visit:", "titleClientUrl": "Client URL", "titleMobileSolution": "Mobile Solution", "titleServerUrl": "Server URL", "titleServicesUrl": "Services URL"}, "moduleDescriptionDashboard": "YTWO Dashboard", "moduleDescriptionIndexTable": "Index Table", "moduleDescriptionLog": "Log", "moduleDescriptionPpsEventConfig": "PPS Event Configuration", "moduleDescriptionPPSHeader": "PPS Header", "moduleDescriptionWorkflowAdministration": "Workflow Administration", "moduleDescriptionWorkflowEngine": "Workflow Designer", "navBarMarketplaceDesc": "Marketplace", "formConfigDialogSubTitle": "Form Structure", "moduleDisplayNameDocumentCentral": "Document Central Query", "entityPinnedDefaultEntity": "<PERSON><PERSON>", "entityPinnedMessage": "{{entity}} pinned.", "moduleDescriptionControllingEnterprise": "Cross-project Controlling", "mainMenuChangePassword": "Change Password", "filterdefConfirmDeleteTitle": "Delete Filter", "filterdefFooterBtnSaveAs": "Save As", "filterdefSaveNamePlaceHolder": "Filter Name", "formConfigDialogTitle": "Customize Form", "formConfigLabelName": "Label Name", "formConfigRestoreBnt": "Rest<PERSON>", "formConfigVisibility": "Visibility", "gridConfigDialogHeader": "Grid Layout", "gridFixedColumnHeader": "Fixed", "mainMenuAbout": "About", "mainMenuContinueBtn": "Continue", "moduleDisplayNameProjectInfoRequest": "RFI", "moduleDisplayNameScheduler": "Scheduler", "moduleDescriptionBank": "The Bank is a financial institution", "moduleDescriptionBasicsConfig": "Module Configuration", "moduleDescriptionBusinessWIC": "Management of Work Item Catalogs", "moduleDescriptionCountry": "Country Description", "moduleDescriptionQTO": "Quantity Takeoff", "moduleDescriptionWip": "Work in Progress", "moduleDisplayNameBusinessOrder": "Orders", "moduleDisplayNameCloudUoM": "UoM (Sample)", "moduleDisplayNameConstructionSystemInstance": "Construction System Instance", "moduleDisplayNameDocumentFolder": "Document Folder", "moduleDisplayNameEvaluationSchema": "Evaluation Schema", "moduleDisplayNameExchangeRates": "Exchange Rates", "moduleDisplayNameRuleDefinitionMaster": "Rules Definition Master", "moduleDisplayNameSalesContract": "Contract Sales", "moduleDisplayNameTables": "Tables", "moduleDisplayNameUsermanagementDescriptor": "Right Descriptor", "moduleDisplayNameWorkCategories": "Work Categories", "moduleDisplayNameWorkflowEngine": "Workflow Designer", "navBarAuditTrailDesc": "Audit Trail", "navBarDiscardDesc": "Discard", "navBarDocuDesc": "Documentation", "navBarRefreshDesc": "Refresh (Ctrl+R)", "navBarSaveDesc": "Save (Ctrl+S)", "navOptionsMenu": "Option Menu", "sdAddProjectFavorite": "Add Project to Favorites", "sdCmdBarLastObjects": "History", "sdCmdBarQuickstart": "Quick Start", "sdCmdBarReport": "Report", "sdCmdBarWizard": "<PERSON>", "sdMainSearchBtnGoogle": "Standard Search", "settingsLanguageEnglishUS": "English (US)", "settingsLanguageGermanDE": "<PERSON><PERSON><PERSON> (DE)", "settingsUILanguage": "UI Language", "tileGroupAdministration": "Administration", "tileGroupEnterprise": "Enterprise", "moduleDisplayNameDashboard": "Dashboard", "moduleDisplayNameProductionPlanning": "Production Planning", "moduleDescriptionResourceReservation": "Resource Reservation", "moduleDisplayNameModelChange": "Model Difference", "moduleDescriptionPpsMaterial": "PPS Material Configuration", "moduleDescriptionPPSItem": "Management of Production Unit", "moduleDescriptionProductionSet": "Management of Production Sets", "moduleDisplayNamePPSItem": "Planning Unit", "moduleDescriptionExternalSystemConfiguration": "External System Configuration", "moduleDescriptionTrsPackage": "Management of Transport Package", "moduleDisplayNamePpsMounting": "Installation", "moduleDescriptioniTWOcity": "iTWO City", "moduleDisplayNameEquipmentGroup": "Plant Group", "moduleDisplayNameEquipmentCatalogs": "Plant Catalogs", "moduleDescriptionNameLogisticPriceCondition": "Logistic Price Condition", "moduleDescriptionNameLogisticSundryService": "Logistic Sundry Service", "moduleDescriptionLogisticSettlement": "Logistic Settlement", "moduleDescriptionNameLogisticSundryServiceGroup": "Sundry Service Group", "moduleDescriptionNameDashboards": "Powered by PowerBI", "moduleDisplayNameDashboards": "Dashboards", "moduleDescriptionTimekeepingEmployee": "Timekeeping Employee", "moduleDescriptionTimekeepingShiftModel": "Timekeeping Shift Model", "moduleDisplayNameYTWODeliveryNotice": "Delivery Notice", "moduleDisplayNameYTWOInvoiceNotice": "Invoice Notice", "moduleDescriptionNameDashboardConfiguration": "Dashboard Configuration", "moduleDescriptionRegionCatalog": "Region Catalog", "moduleDescriptionResourcesSkill": "Resource Skills", "moduleDescriptionWorkOperationTypes": "Work Operation Types", "moduleDescriptionMap": "Model Map", "moduleDisplayNameMap": "Map", "moduleDescriptionLogisticCard": "Job Cards", "moduleDescriptionLogisticCardTemplate": "Job Card Templates", "moduleDisplayNameResourceMaintenance": "Plant Maintenance Schemes", "moduleDisplayNameIndexTable": "Index Table", "moduleDisplayNameProductTemplates": "Product Templates", "moduleDisplayNameProductTemplate": "Product Template", "moduleDisplayNameProductionPlace": "Production Place", "moduleDescriptionProductionPlace": "Management of Production Place", "moduleDisplayNameControllingActuals": "Actuals", "moduleDisplayNameInventory": "Inventory", "moduleDisplayNameBasicsRiskRegister": "Risk Register", "moduleDescriptionCloudUitesting": "Cloud UITesting", "moduleDisplayNameOrderProposals": "Order Proposals", "moduleDescriptionDocumentCentral": "Document Central Query", "moduleDisplayNameControllingEnterprise": "Enterprise Controlling", "bim360": {"language": {"english": "English", "german": "German"}, "projectType": {"airport": "Airport", "assistedLivingNursingHome": "Assisted Living / Nursing Home", "bridge": "Bridge", "canalWaterway": "Canal / Waterway", "commercial": "Commercial", "conventionCenter": "Convention Center", "courtHouse": "Court House", "damsFloodControlReservoirs": "Dams / Flood Control / Reservoirs", "dataCenter": "Data Center", "demonstrationProject": "Demonstration Project", "dormitory": "Dormitory", "educationFacility": "Education Facility", "governmentBuilding": "Government Building", "harborRiverDevelopment": "Harbor / River Development", "healthcare": "Healthcare", "hospital": "Hospital", "hotelMotel": "Hotel / Motel", "industrialEnergy": "Industrial & Energy", "infrastructure": "Infrastructure", "institutional": "Institutional", "library": "Library", "manufacturingFactory": "Manufacturing / Factory", "medicalLaboratory": "Medical Laboratory", "medicalOffice": "Medical Office", "militaryFacility": "Military Facility", "multiFamilyHousing": "Multi-Family Housing", "museum": "Museum", "oilGas": "Oil & Gas", "outPatientSurgeryCenter": "OutPatient Surgery Center", "parkingStructureGarage": "Parking Structure / Garage", "performingArts": "Performing Arts", "plant": "Plant", "powerPlant": "Power Plant", "prisonCorrectionalFacility": "Prison / Correctional Facility", "rail": "Rail", "recreationBuilding": "Recreation Building", "religiousBuilding": "Religious Building", "researchFacilityLaboratory": "Research Facility / Laboratory", "residential": "Residential", "retail": "Retail", "sampleProjects": "Sample Projects", "seaport": "Seaport", "singleFamilyHousing": "Single-Family Housing", "solarFar": "Solar Far", "stadiumArena": "Stadium/Arena", "streetsRoadsHighways": "Streets / Roads / Highways", "templateProject": "Template Project", "themePark": "Theme Park", "trainingProject": "Training Project", "transportationBuilding": "Transportation Building", "tunnel": "Tunnel", "utilities": "Utilities", "warehouse": "Warehouse (non-manufacturing)", "wasteWaterSewers": "Waste Water / Sewers", "waterSupply": "Water supply", "windFarm": "Wind Farm", "office": "Office"}, "serviceType": {"collab": "Design Collaboration", "doc_manager": "Document Management", "field": "BIM 360 Field", "glue": "BIM 360 Glue", "pm": "Project Management", "fng": "Field Management", "gng": "Model Coordination"}}, "moduleDisplayNameEfbSheets": "EFB Sheets", "moduleDescriptionCrewMixes": "Crew Mixes", "moduleDisplayNameCrewMixes": "Crew Mixes", "moduleDescriptionEfbSheets": "EFB Sheets", "moduleDisplayNamePpsActivity": "Installation Activity", "log": "Log", "moduleDiscriptionyNameTimekeepingEmployee": "Timekeeping Employee", "moduleDiscriptionyNameTimekeepingShiftModel": "Timekeeping Shift Model", "moduleDiscriptionyNameTimekeepingTimeSymbols": "Timekeeping Time Symbols", "moduleDisplayNameEngineeringCADImportConfiguration": "Engineering CAD Import Configuration", "moduleDescriptionEngineeringCADImport": "Management of Engineering CAD Import", "moduleDisplayNameEngineeringCADImport": "Engineering CAD Import", "moduleDescriptionEngineeringCADImportConfig": "Management of Engineering CAD Import Configuration", "moduleDescriptionEngineeringCADImportConfiguration": "Management of Engineering CAD Import Configuration", "moduleDisplayNameEngineeringCADImportConfig": "Engineering CAD Import Configuration", "moduleDisplayNameBasicsTaxCode": "Tax Code", "moduleDisplayNameUserForm": "User Forms", "moduleDisplayNameDependentData": "User Container", "moduleDescriptionProductionplanningFabricationunit": "Management of Fabrication Unit", "moduleDisplayNameProductionplanningFabricationunit": "Fabrication Unit", "moduleDescriptionNameBIPlusDesigner": "BI+ Designer", "moduleDisplayNameBIPlusDesigner": "BI+ Designer", "contractApproval": {"approve": "Approve Contract", "approveSuccess": "The Contract is approved", "reject": "Reject Contract", "rejectSuccess": "The Contract is rejected", "sureApprove": "Are you sure you want to approve this Contract?", "sureReject": "Are you sure you want to reject this Contract?", "reason": "Reason", "info": "Info", "approvalConfigErrorText": "No suitable Approval Config found for your current Clerk Role, please check the Procurement Configuration", "approvalConfigNotFoundError": "Approval Configuration Error", "approvalConfigNotFoundErrorText": "No suitable Approval Config found for your current Clerk Role which can approve", "approvalConfigError": "Approval Configuration Error", "contractApprovalWizardTitleName": "Contract Approval Wizard", "contractConfirmWizardTitleName": "Contract Confirm Wizard", "contractRejectWizardTitleName": "Contract Reject Wizard", "sendWorkflowError": "Contract Confirm/Reject Wizard Send Email Error", "rejectToLevel": "Reject Contract to level", "rejectToLevelSuccess": "The Contract is rejected to {{rejectedLevel}} level", "sureRejectToLevel": "Are you sure you want to reject this Contract to a previous level?"}, "externalSystemCredential": {"createExternalSourceToUser": "Create New External Source To User", "currentUser": "Add External System Credentials", "deleteExternalSourceToUser": "Delete External Source To User", "dialogTitle": "External System Credentials", "externalSystemCredentialListStepTitle": "External Source To User", "noUserFound": "There is no user in the database"}, "moduleDisplayNameEquipment": "Plant Master", "moduleDisplayNameCheckList": "Check List", "moduleDisplayNameHsqeCheckListTemplate": "Check List Template", "tileGroupHQSE": "Health, Safety, Environment, Quality (HSQE)", "moduleDisplayNameCheckListTemplate": "Check List Template", "moduleDisplayNameTimekeepingPaymentGroup": "Payment Groups", "moduleDescriptionTimekeepingPaymentGroup": "Payment Groups", "navBarRefreshSelDesc": "Refresh Selected Entities", "moduleDescriptionPrivacyMain": "Privacy Request", "moduleDisplayNamePrivacyMain": "Privacy Request", "rfqBidder": {"businessPartnerNotFoundError": "Business Partner Error", "businessPartnerNotFoundErrorText": "No business partner with communication channel Email was found for the selected rfq.", "startingClerkNotFoundError": "Starting Clerk <PERSON><PERSON>r", "startingClerkNotFoundErrorText": "No clerk was found.", "businessPartnerDefaultNotFoundDefaultErrorText": "You are using a communication channel, that is not supported by this wizard.", "businessPartnerNotFoundPortalErrorText": "No business partner with communication channel Portal was found for the selected rfq.", "rfqBidderWizardTitleName": "Rfq Bidder Wizard", "sendWorkflowError": "Rfq Bidder Wizard Send <PERSON><PERSON>r", "sendWorkflowErrorText": "Something went wrong while sending the email(s), please retry sending or contact your administrator.", "settingsValidationError": "Setting Validation Error", "settingsValidationErrorText": "Something went wrong while validating your wizard settings, please retry or close and open the wizard again."}, "contractConfirm": {"conConfirmWizardTitle": "Contract Confirm Wizard"}, "contractApprovalWizard": {"conApprovalWizardTitle": "Contract Approval Wizard"}, "moduleDescriptionModelAnnotation": "Remarks and Discussions on Models", "moduleDisplayNameModelAnnotation": "Model Annotations", "pinningContext": "Pinning Context", "moduleDisplayNameAIConfiguration": "AI Configuration", "sdSelectAllCond": "Select all / Select none", "moduleDescriptionAIConfiguration": "AI Configuration", "moduleDescriptionProjectInfoRequest": "Request for Information", "moduleDescriptionTimekeepingWorkTimeModel": " Working Time Model", "moduleDisplayNameTimekeepingWorkTimeModel": "Working Time Model", "infoTextMaxPageSize": "The maximum value has been set to {{maxPageSize}}", "moduleDescriptionMtwoChatbot": "<PERSON><PERSON>", "moduleDisplayNameMtwoChatbot": "<PERSON><PERSON>", "statusbarFilter": {"loadModuleViewDescription": "The option to load data when starting the module is active. The option can be deactivated in the View Save dialogue.", "loadTabViewDescription": "The option to Load data after a tab change is active. The option can be deactivated in the View Save dialogue.", "loadViewTitle": "Automatic loading is active", "pinnedViewDescription": "Assigned filter:<br> {{pinnedName}}<br><br> A filter is assigned to the view. The option can be deactivated in the Save View dialogue.", "pinnedViewTitle": "View filter is active"}, "dialogDeactivate": "Don’t show this message again", "navBarResetDeactivateMsg": "Restore hint and warning dialogues", "navBarResetDeactivateDialogMsg": "Would you like to restore all hint and warning dialogues in this module?", "sdCmdBarChatBot": "chatBot", "infoTextUnlimitedPageSize": "Maximum value unlimited", "moduleDisplayNameControllingRevenueRecognition": "Revenue Recognition", "moduleDescriptionTimekeepingTimeallocation": "Allocation of productive hours", "moduleDisplayNameTimekeepingTimeallocation": "Time Allocation", "InfoTextTooltip": "The value can be defined in the module <PERSON><PERSON><PERSON>, Container Module, Column <PERSON>", "moduleDisplayNameBasicsSalesTaxCode": "US Sales Tax Code", "moduleDisplayNameControllingGeneralContractor": "General Contractor Controlling", "moduleDescriptionMeeting": "Meeting Management", "moduleDisplayNameMeeting": "Meeting Management", "moduleDescriptionNamePpsProcessConfig": "PPS Process Configuration", "moduleDisplayNamePpsProcessConfig": "PPS Process Configuration", "moduleDescriptionControllingUnitTemplate": "Management of controlling unit templates", "moduleDisplayNameControllingUnitTemplate": "Controlling Unit Templates", "moduleDescriptionPpsFormwork": "Management of Formwork", "moduleDisplayNamePpsFormwork": "Formwork", "moduleDescriptionNamePpsStrandPattern": "One Prefab Strand Pattern", "moduleDisplayNamePpsStrandPattern": " One Prefab Strand Pattern", "moduleDescriptionEngineeringDrawingType": "Management of Engineering Drawing Type", "moduleDisplayNameEngineeringDrawingType": "Engineering Drawing Type", "editModeProjectFavorite": "Edit Mode", "moduleDescriptionEngineeringFormulaConfig": "Management of Formula Configuration", "moduleDisplayNameEngineeringFormulaConfig": "Formula Configuration", "moduleDescriptionPpsFormulaConfig": "Management of Formula Configuration", "moduleDisplayNamePpsFormulaConfig": "Formula Configuration", "moduleDescriptionSchedulerUI": "Scheduler Management", "moduleDisplayNameSchedulerUI": "Scheduler", "header": {"certificateInfo": "Please check SSL and Identityserver Certificate(s). Navigate to Settings => Alert Messages", "announcements": "Announcements", "help": "Help and Support", "knownIssues": "Known Issues", "notifications": "Notification", "performanceIssues": "Performance Issues?", "profile": "Profile and Company Settings", "relaeaseNotes": "Release Notes", "whatsnew": "What's New", "collaborators": "Collaborators"}, "api": {"companychkerror1": "Token is valid, but Company check for Company={{p1}} with roleId={{p2}} failed.", "companychkerror2": "Token is valid, but Company check for Company={{p1}} with roleId={{p2}} failed. Reason is => {{p3}});", "naverrinquiry": "API Inquiry or LookUp Request failed! NavInfo requires company, which is missing.", "naverrtitle": "API Navigation Parameter not valid!"}, "moduleDescriptionModelMeasurements": "Measurements in Models", "moduleDisplayNameModelMeasurements": "Model Measurements", "moduleDescriptionTimekeepingTimeControlling": "Review times", "moduleDisplayNameTimekeepingTimeControlling": "Time Controlling", "moduleDisplayNameTimekeepingSettlement": "Timekeeping Settlement", "moduleDescriptionTimekeepingSettlement": "Intercompany Settlements for Timekeeping", "statusbar": {"gridSearchDescription": "Displays the search result of the grid search.", "gridSearchTitle": "Grid search", "sidebarSearchDescription": "Search result from the database. Depending on the number of hits and the option 'Records per Page', the result list is displayed on the several result pages.", "sidebarSearchTitle": "Sidebar search"}, "moduleDescriptionLogisticplantsupplier": "Management of Logistic Plant Supplier", "moduleDescriptionNameShankar": "<PERSON>", "moduleDescriptionNameWinjit": "<PERSON><PERSON>", "moduleDisplayNameLogisticPlantsupplier": "Logistic Plant Supplier", "moduleDisplayNameShankar": "<PERSON>", "moduleDisplayNameWinjit": "<PERSON><PERSON>", "template": {"grouping": {"action": "Action", "noGrouping": "No grouping", "startDate": "Start Date", "status": "Status", "type": "Type"}, "sorting": {"noSorting": "No Sorting", "startDate": {"asc": "By start date (Asc.)", "desc": "By start date (Desc.)"}, "status": {"asc": "Status (Asc.)", "desc": "Status (Desc.)"}}}, "moduleDescriptionProjectProjectGroup": "Management of Projects", "moduleDisplayNameProjectProjectGroup": "Project Group", "pinningDesktopDialogAddInPage": "The new tile was created in the desktop page '{{pagename}}'.", "pinningDesktopDialogHeader": "Pin to Desktop as a Tile", "pinningDesktopDialogNewPage": "A new desktop page has been created.", "pinningDesktopDialogSelectionPage": "Selection Desktop-Pages:", "pinningDesktopDialogTopDescription": "Select the page on which the new tile is to be added", "moduleDescriptionProjectGroup": "Management of Projects", "moduleDisplayNameProjectGroup": "Project Group", "sdGoogleRadiusSearchChk": "Location Search", "indexTable": "Index Table", "moduleDescriptionPpsCostCodes": "One Prefab Cost Codes Configuration", "moduleDisplayNamePpsCostCodes": "One Prefab Cost Codes Configuration", "formAboutDataLanguage": "Data Language: {{p1}}", "formAboutUiLanguage": "UI Language: {{p1}}", "titleAllRecord": "All records are loaded from the database", "titleCurrentSearch": "Current Search Function: ", "titleMatchRecord": "Only records that match the search criteria are loaded", "titleNoSearchTerm": "No search term available", "titleSearchCriteria": "Search Criteria: ", "titleSelectedForm": "Selected Form: ", "titleSelectedSearch": "Selected Search: ", "googleRadiusSearchAddressInvalidWarning": "The address is invalid! Please edit the address!", "layoutExport": {"name": "Name", "selected": "Selected", "type": "Type"}, "formConfigClearBnt": "Clear all", "moduleDisplayNameWip": "WIP", "moduleDisplayNameBid": "Bid", "moduleDescriptionTimekeepingCertificate": "Management of Employee Certificates", "moduleDisplayNameTimekeepingCertificate": "Employee Certificate", "moduleDisplayNameControllingConfiguration": "Controlling Configuration", "moduleDisplayNameControllingProjectControls": "Project Controls", "sidebarNotification": {"additionalDetailsHeaders": {"additionDetailsTitle": "Additional Details", "GenericWizardBidderHeader": "B<PERSON>ders"}, "panelTitle": "Notification", "showReportBtnTitle": "Show Report", "tabs": {"all": "All", "import": "Import", "report": "Report", "scheduler": "Scheduler", "workflow": "Workflow"}}, "rfqApproval": {"approve": "Approve RFQ", "approveSuccess": "The Rfq is approved", "reject": "Reject Rfq", "rejectSuccess": "The Rfq is rejected", "rejectToLevel": "Reject Rfq to level", "rejectToLevelSuccess": "The Rfq is rejected to level {{rejectedLevel}}", "rfqApprovalWizardTitleName": "Rfq Approval Wizard", "sureApprove": "Are you sure you want to approve this RFQ?", "sureReject": "Are you sure you want to reject this RFQ?", "sureRejectToLevel": "Are you sure you want to reject this RFQ to a previous level?"}, "sdGoogleBelongToProfitCenterChk": "Belonging to this Profit Center", "moduleDescriptionResourcePlantpricing": "Management of Plant Pricing", "moduleDisplayNameResourcePlantpricing": "Plant Pricing", "sdCmdBarOutlook": "Outlook", "mainMenuProfile": "My Profile", "outlook": {"cc": "CC", "subject": "Subject", "to": "To", "attachFile": "Attach File", "attachReport": "Attach Report", "delete": "Delete", "draft": "Draft", "inbox": "Inbox", "login": "<PERSON><PERSON>", "new": "New Item", "newMsg": "New Message", "openInOutlook": "Open in Outlook", "refresh": "Refresh", "save": "Save", "send": "Send", "switchAccountAndFolder": "Switch Account and Folder", "title": "Outlook", "close": "Close", "reportParameters": "Report Parameters", "reportsTitle": "Insert Report into Email", "selectReport": "Select Report", "noEmailTip": "We didn't find anything to show here", "allEmail": "All Mail", "date": "Date", "detail": "Detail", "edit": "Edit", "from": "From", "goTo": "Go to Outlook", "halfYear": "Half Year", "maxImageSizeLimit": "The image size exceeds the maximum limit({{max}} bytes), please upload it as an attachment!", "newestOnTop": "Newest on Top", "oldestOnTop": "Oldest on Top", "oneMonth": "One Month", "oneWeek": "One Week", "oneYear": "One Year", "unReadEmail": "Unread Mail", "showMore": "Show More", "aTozOnTop": "A to Z on Top", "groupBy": "Group by", "noLeadEntity": "Please select a lead entity first!", "zToaOnTop": "Z to A on Top", "noParameters": "No Parameters", "generateFailedContent": "The template file could not be found", "generateFailedTitle": "Generate Failed", "generateAttachment": "Generate as attachment", "generateContent": "Generate into Content", "viewMore": "Click to view more"}, "moduleDescriptionPlantEstimation": "Management of Plants for Estimation", "moduleDisplayNamePlantEstimation": "Plant Estimation", "sidebarToolMaxIcon": "Maximize", "sidebarToolMinIcon": "Minimize", "KeyboardShortcutsOverview": "Keyboard Shortcuts", "moduleDescriptionDefectInfo": "Management of Defects", "sdCmdBarPrjNavi": "Project Navigator", "navBarProductDocuDesc": "Product Documentation", "timekeeping": {"clocking": "Clock your times for timekeeping", "absenceForEmployee": "Employee Absence"}, "keyboard": {"alt": "Alt", "ctrl": "Ctrl", "del": "DEL", "down": "Arrow Key Down", "end": "End", "escape": "ESC", "left": "Arrow Key Left", "minus": "Minus", "pagedown": "Page Down", "pageup": "Page Up", "plus": "Plus", "right": "Arrow Key Right", "shift": "Shift", "space": "Space", "tab": "Tab", "up": "Arrow Key Up"}, "moduleDescriptionLogisticAction": "Management of Logistic Action", "moduleDisplayNameLogisticAction": "Action Item Templates", "sdGoogleIncludeRefLineItemsChk": "Include referenced Line Items", "moduleDescriptionProjectDropPoints": "Organization of Drop Points", "moduleDisplayNameProjectDropPoints": "Project Area", "collaboratorNA": "N/A", "employeeAbsence": {"createAbsence": "Create new Absence", "deleteAbsence": "Delete Absence", "entityFromDateTime": "From", "entityFromTime": "From Time", "entityTimeSymbol": "Time Symbol", "entityToDateTime": "To", "entityToTime": "To Time", "entityVacationBalance": "Vacation Balance", "newVacationBalance": "Vacation Balance after Request", "okBtnText": "Send Absence Request", "PlannedAbsenceStatusFk": "Planned Absence Status", "planningAbsence": "Planning Absence or Vacation"}, "employeeClocking": {"clockingTime": "Clock your time", "endBreakBtn": "End Break", "endWorkBtn": "End Work", "errorLoginEmployee": "The logged user is not allowed for clocking or he is assigned to more than one employee!", "errorNoTimeSymbol": "No Time Symbol for reporting found. Please go to the module Time Symbols and select the checkbox “Is Reporting” for at least one time symbol.", "onBreak": "You are currently on a break", "recordTime": "Record your time", "startBreakBtn": "Start Break", "startWorkBtn": "Start Work", "successMsg": "Time recorded!", "surchargeDialog": "What surcharges do apply?", "travelDistance": "Travel Distance to Site", "travelHome": "Travel Home", "travelTime": "Travel Time to Site", "working": "You are currently working"}, "activeInModule": "Active in Module", "moduleDisplayNameAdvancedWorkPackaging": "Advanced Work Packaging", "notification": {"menuTabs": {"all": "All", "report": "Report", "workflow": "Workflow", "import": "Import", "schedular": "Schedular"}, "header": "Notification", "showReport": " Show Report"}, "sdCmdBarNotification": "Notifications", "moduleDisplayNameInfoRequest": "RFI", "moduleDescriptionInfoRequest": "Management of Info Request", "sidebar": {"workflow": {"entity": "Entity", "owner": "Owner", "keyUser": "Key User", "groupDefinition": {"noEntity": "No Entity", "noOwner": "No Owner", "noKeyUser": "No Key User"}}}}}}