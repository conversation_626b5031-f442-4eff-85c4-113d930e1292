{"name": "ui-interfaces", "$schema": "..\\..\\..\\node_modules\\nx\\schemas\\project-schema.json", "projectType": "library", "sourceRoot": "libs/ui/interfaces/src", "prefix": "ui-interfaces", "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/ui/interfaces"], "options": {"jestConfig": "libs/ui/interfaces/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}, "tags": []}