/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';
import { ProcurementContractBoqDataService } from './procurement-contract-boq.service';
import { IConHeaderEntity } from '@libs/procurement/interfaces';
import { ProcurementCommonBoqItemDataServiceBase } from '@libs/procurement/common';
import { ContractComplete } from '../model/contract-complete.class';
import { ProcurementContractBoqItemReadonlyProcessor } from './processors/procurement-contract-boq-item-readonly-processor.service';


@Injectable({ providedIn: 'root' })
export class ProcurementContractBoqItemDataService extends ProcurementCommonBoqItemDataServiceBase<IConHeaderEntity, ContractComplete> {

	public constructor(private contractBoqDataService: ProcurementContractBoqDataService) {
		super(contractBoqDataService);
	}

	protected override createReadonlyProcessor() {
		return new ProcurementContractBoqItemReadonlyProcessor(this);
	}
}
