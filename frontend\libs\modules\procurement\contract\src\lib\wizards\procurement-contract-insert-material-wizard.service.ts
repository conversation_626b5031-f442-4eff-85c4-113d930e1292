/*
 * Copyright(c) RIB Software GmbH
 */
import { inject, Injectable } from '@angular/core';
import { PlatformConfigurationService, PlatformHttpService, PlatformTranslateService } from '@libs/platform/common';
import { UiCommonMessageBoxService, StandardDialogButtonId, IFormDialogConfig, FieldType, createLookup, UiCommonFormDialogService, IDialogErrorInfo, ILookupContext } from '@libs/ui/common';
import { ProcurementContractHeaderDataService } from '../services/procurement-contract-header-data.service';
import { BasicsSharedMaterialCatalogLookupService, BasicsSharedMaterialDiscountGroupLookupService, BasicsSharedMaterialGroupLookupService } from '@libs/basics/shared';
import { isNull, isUndefined } from 'lodash';
import { IMaterialDiscountGroupLookupEntity, IMaterialGroupLookupEntity } from '@libs/basics/interfaces';

interface IInsertMaterialOptions {
	PrcHeaderFK: number;
	MaterialCatalogFk: number | null;
	MaterialGroupFk: number | null;
	MaterialDiscountGroupFk: number | null;
}

@Injectable({
	providedIn: 'root',
})
export class ProcurementContractInsertMaterialWizardService {
	protected readonly messageBoxService = inject(UiCommonMessageBoxService);
	protected readonly formDialogService = inject(UiCommonFormDialogService);
	private readonly translateService = inject(PlatformTranslateService);
	private readonly http = inject(PlatformHttpService);
	protected readonly configService = inject(PlatformConfigurationService);
	protected readonly dataService = inject(ProcurementContractHeaderDataService);
	private isMaterialGroupReadonly: boolean = true;

	public async onStartWizard() {
		const selectedItem = this.dataService.getSelectedEntity();
		if (!selectedItem) {
			const errorInfo: IDialogErrorInfo = {
				errorCode: 0,
				errorVersion: '',
				errorMessage: this.translateService.instant('procurement.contract.noSelectRecord').text,
				detailMethod: null,
				detailMessage: '',
				detailStackTrace: '',
				errorDetail: '',
			};
			await this.messageBoxService.showErrorDialog(errorInfo);
			return;
		}
		const options: IFormDialogConfig<IInsertMaterialOptions> = {
			entity: {
				PrcHeaderFK: selectedItem?.PrcHeaderFk,
				MaterialCatalogFk: null,
				MaterialGroupFk: null,
				MaterialDiscountGroupFk: null,
			},
			id: 'insertMaterial',
			headerText: this.translateService.instant('procurement.common.wizard.insertMaterial.insertMaterialTitle').text,
			formConfiguration: {
				formId: 'insertMaterialWizard',
				showGrouping: false,
				rows: [
					{
						id: 'materialCatalogFk',
						model: 'MaterialCatalogFk',
						type: FieldType.Lookup,
						required: true,
						lookupOptions: createLookup({
							dataServiceToken: BasicsSharedMaterialCatalogLookupService,
						}),
						label: {
							key: 'basics.material.reference.entityMdcMaterialCatalogFk',
						},
						change: (e) => {
							this.isMaterialGroupReadonly = isNull(e.newValue) || isUndefined(e.newValue);
						},
					},
					{
						id: 'materialGroupFk',
						model: 'MaterialGroupFk',
						type: FieldType.Lookup,
						required: true,
						lookupOptions: createLookup({
							dataServiceToken: BasicsSharedMaterialGroupLookupService,
							serverSideFilter: {
								key: 'insert-material-group-filter',
								execute(context: ILookupContext<IMaterialGroupLookupEntity, IInsertMaterialOptions>) {
									return { MaterialCatalogFk: context.entity?.MaterialCatalogFk };
								},
							},
						}),
						label: {
							key: 'basics.material.record.materialGroup',
						},
					},
					{
						id: 'materialDiscountGroupFk',
						model: 'MaterialDiscountGroupFk',
						type: FieldType.Lookup,
						required: true,
						lookupOptions: createLookup({
							dataServiceToken: BasicsSharedMaterialDiscountGroupLookupService,
							serverSideFilter: {
								key: 'procurement-contract-material-discount-group-filter',
								execute(context: ILookupContext<IMaterialDiscountGroupLookupEntity, IInsertMaterialOptions>) {
									return 'MaterialCatalogFk=' + context.entity?.MaterialCatalogFk;
								},
							},
						}),
						label: {
							key: 'procurement.common.wizard.insertMaterial.discountGroup',
						},
					},
				],
			},
			buttons: [
				{
					id: StandardDialogButtonId.Ok,
					caption: { key: 'ui.common.dialog.okBtn' },
					isDisabled: () => {
						const { MaterialCatalogFk, MaterialGroupFk, MaterialDiscountGroupFk } = options.entity;
						const allSet = [MaterialCatalogFk, MaterialGroupFk, MaterialDiscountGroupFk].every((v) => v != null);
						return !allSet;
					},
				},
			],
		};
		const callResult = await this.formDialogService.showDialog<IInsertMaterialOptions>(options);
		if (callResult?.closingButtonId === StandardDialogButtonId.Ok) {
			const entity = options.entity;
			const prcHeader = this.dataService.getSelectedPrcHeaderEntity();

			const requestPayload = {
				PrcHeaderFK: prcHeader.Id,
				materialcatalogfk: entity.MaterialCatalogFk,
				materialgroupfk: entity.MaterialGroupFk,
				materialdiscountgroupfk: entity.MaterialDiscountGroupFk,
			};
			try {
				await this.http.post('procurement/contract/wizard/updateMaterial', requestPayload);

				this.messageBoxService.showInfoBox(this.translateService.instant('procurement.common.wizard.insertMaterial.insertMaterialSucceed').text, 'info', true);
			} catch (error) {
				this.messageBoxService.showErrorDialog(this.translateService.instant('procurement.common.wizard.insertMaterial.insertMaterialFailed').text);
			}
		}
	}
}
