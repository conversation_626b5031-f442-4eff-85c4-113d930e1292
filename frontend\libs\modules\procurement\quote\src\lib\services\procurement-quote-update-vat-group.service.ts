/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import { ProcurementCommonUpdateVatGroupService } from '@libs/procurement/common';
import { ProcurementQuoteHeaderDataService } from '../services/quote-header-data.service';
import { ProcurementQuoteRequisitionDataService } from '../services/quote-requisitions-data.service';
import { QuoteHeaderEntityComplete } from '../model/entities/quote-header-entity-complete.class';
import { IQuoteHeaderEntity } from '../model/entities/quote-header-entity.interface';

/**
 * Procurement quote update vat group service
 */
@Injectable({
	providedIn: 'root'
})
export class ProcurementQuoteUpdateVatGroupService extends ProcurementCommonUpdateVatGroupService<IQuoteHeaderEntity, QuoteHeaderEntityComplete> {
	protected readonly qtnRequisitionService = inject(ProcurementQuoteRequisitionDataService);

	/**
	 * The constructor
	 * @protected
	 */
	protected constructor() {
		const dataService = inject(ProcurementQuoteHeaderDataService);
		super(dataService, dataService.getInternalModuleName());
	}

	/**
	 * Get prcHeaderIds
	 * Quote maybe has multiple prcHeaderId
	 * @param entity
	 * @protected
	 */
	protected override getPrcHeaderIds(entity: IQuoteHeaderEntity): number[] {
		const qtnRequisitionList = this.qtnRequisitionService.getList();
		return qtnRequisitionList?.filter(e => e.QtnHeaderFk === entity.Id)?.map(e => e.PrcHeaderFk) ?? [];
	}
}