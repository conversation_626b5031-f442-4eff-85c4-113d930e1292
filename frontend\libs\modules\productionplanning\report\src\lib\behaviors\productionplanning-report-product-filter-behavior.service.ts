/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable, InjectionToken } from '@angular/core';
import { IPpsProductEntityGenerated } from '@libs/productionplanning/shared';
import { IEntityContainerBehavior, IGridContainerLink } from '@libs/ui/business-base';

export const PRODUCTIONPLANNING_REPORT_PRODUCT_FILTER_BEHAVIOR_TOKEN = new InjectionToken<ProductionplanningReportProductFilterBehavior>('productionplanningReportProductFilterBehavior');

@Injectable({
	providedIn: 'root'
})
export class ProductionplanningReportProductFilterBehavior implements IEntityContainerBehavior<IGridContainerLink<IPpsProductEntityGenerated>, IPpsProductEntityGenerated> {

}