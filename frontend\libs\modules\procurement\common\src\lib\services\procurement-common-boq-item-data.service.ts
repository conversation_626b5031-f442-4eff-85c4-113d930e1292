/*
 * Copyright(c) RIB Software GmbH
 */

import { BoqItemDataServiceBase, IBoqParentCompleteEntity, IBoqParentEntity } from '@libs/boq/main';
import { IBoqItemEntity } from '@libs/boq/interfaces';
import { IPrcBoqExtendedEntity } from '@libs/procurement/interfaces';
import { ProcurementCommonBoqDataServiceBase } from './procurement-common-boq.service';
import { ProcurementCommonBoqItemReadonlyProcessor } from '../boq/services/procurement-common-boq-item-readonly-processor.service';

export class ProcurementCommonBoqItemDataServiceBase<PT extends IBoqParentEntity, PU extends IBoqParentCompleteEntity> extends BoqItemDataServiceBase {
	public readonly readonlyProcessor: ProcurementCommonBoqItemReadonlyProcessor<PT, PU>;

	public constructor(private parentService: ProcurementCommonBoqDataServiceBase<PT, PU>) {
		const options = BoqItemDataServiceBase.createDataServiceOptions(parentService);
		super(options);

		this.readonlyProcessor = this.createReadonlyProcessor();
		this.processor.addProcessor([this.readonlyProcessor]);
	}

	/*
	 *	Override this method to provide module specific readonly processor. Otherwise the common processor is used.
	 */
	protected createReadonlyProcessor(): ProcurementCommonBoqItemReadonlyProcessor<PT, PU> {
		const processor = new ProcurementCommonBoqItemReadonlyProcessor(this);
		processor.initializeProcess().then();
		return processor;
	}

	public override getSelectedBoqHeaderId(): number | undefined {
		return this.parentService.getSelectedEntity()?.BoqRootItem?.BoqHeaderFk ?? undefined;
	}

	public override isParentFn(parentKey: IPrcBoqExtendedEntity, entity: IBoqItemEntity): boolean {
		return entity.BoqHeaderFk === parentKey.Id;
	}

	public isParentEntityReadonly(){
		return this.parentService.isParentEntityReadonly();
	}
}
