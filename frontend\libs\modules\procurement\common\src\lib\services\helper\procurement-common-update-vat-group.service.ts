/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import { StandardDialogButtonId, UiCommonMessageBoxService } from '@libs/ui/common';
import { IParentRole, ValidationInfo, ValidationResult } from '@libs/platform/data-access';
import { ProcurementCommonWizardUtilService } from '../wizards';
import {
	CompleteIdentification,
	INavigationBarControls,
	PlatformTranslateService,
	PlatformHttpService
} from '@libs/platform/common';
import { IPrcVatGroupEntity } from '../../model/interfaces';

/**
 * Procurement common update vat group service
 */
@Injectable({
	providedIn: 'root'
})
export abstract class ProcurementCommonUpdateVatGroupService<
	T extends IPrcVatGroupEntity,
	U extends CompleteIdentification<T>> {
	private readonly http = inject(PlatformHttpService);
	private readonly translateService = inject(PlatformTranslateService);
	private readonly messageBoxService = inject(UiCommonMessageBoxService);
	private readonly wizardService = inject(ProcurementCommonWizardUtilService);

	protected recalculateUrl = 'procurement/common/updatevatgroup/calculate';

	/**
	 * The constructor
	 * @param dataService
	 * @param moduleName
	 * @protected
	 */
	protected constructor(
		protected dataService: INavigationBarControls & IParentRole<T, U>,
		protected moduleName: string) {
	}

	/**
	 * Get prcHeaderIds
	 * Requisition and Contract module have only one prcHeaderId
	 * Package and Quote module maybe have multiple prcHeaderIds
	 * Pes and Invoice module have no prcHeaderId
	 */
	protected getPrcHeaderIds(entity: T): number[] {
		return entity?.PrcHeaderFk ? [entity.PrcHeaderFk] : [];
	}

	/**
	 * Get contractName of customized recalculate logic after vatGroup updated
	 * @protected
	 */
	protected getContractName() {
		return 'procurement.common';
	}

	/**
	 * Validation of BpdVatGroupFk
	 * @param info
	 */
	public async validateBpdVatGroupFk(info: ValidationInfo<T>): Promise<ValidationResult> {
		const isConfirmed = await this.confirmVatGroupChange();
		if (!isConfirmed) {
			// Not update vatGroupFk, keep old value, so valid is true and apply is false
			return {valid: true, apply: false};
		}

		this.scheduleRecalculation(info);
		return {valid: true, apply: true};
	}

	/**
	 * Changing the VatGroup leads to the recalculation and saving sub data
	 * Open a dialog to ask whether continue
	 * @private
	 */
	private async confirmVatGroupChange(): Promise<boolean> {
		const textOfBody = this.translateService.instant('procurement.common.changeVatGroupRecalBoqAndItem').text;
		const textOfHeader = this.translateService.instant('basics.common.alert.warning').text;

		const result = await this.messageBoxService.showYesNoDialog(textOfBody, textOfHeader);
		return result.closingButtonId == StandardDialogButtonId.Yes;
	}

	/**
	 * Schedules the recalculation process asynchronously
	 * @param info Validation context
	 */
	private scheduleRecalculation(info: ValidationInfo<T>): void {
		setTimeout(() => this.updateVatGroupAndRecalculate(
			info.entity,
			info.value as number | null | undefined
		));
	}

	/**
	 * Firstly update entity.BpdVatGroupFk,
	 * and then save header,
	 * finally send recalculate request, recalculate children data in backend.
	 * Refresh selected item and reload children after recalculation finish
	 * @param entity
	 * @param newVatGroup
	 * @private
	 */
	private async updateVatGroupAndRecalculate(entity: T, newVatGroup?: number | null) {
		this.wizardService.showLoadingDialog(this.translateService.instant('procurement.common.processing').text);

		entity.BpdVatGroupFk = newVatGroup;
		await this.dataService.save();
		await this.recalculateRequest(entity);
		await this.dataService.loadChildEntities(entity);
		await this.dataService.refreshSelected();

		this.wizardService.closeLoadingDialog();
	}

	/**
	 * Send recalculate request
	 * @private
	 */
	private async recalculateRequest(entity: T) {
		return await this.http.post<void>(this.recalculateUrl, this.getRecalculateParam(entity));
	}

	/**
	 * Get recalculate request parameter
	 * @param entity
	 * @private
	 */
	private getRecalculateParam(entity: T) {
		return {
			HeaderId: entity.Id,
			ModuleName: this.moduleName,
			ContractName: this.getContractName(),
			PrcHeaderIds: this.getPrcHeaderIds(entity),
			VatGroupId: entity.BpdVatGroupFk,
			HeaderTaxCodeId: entity.TaxCodeFk,
			ExchangeRate: entity.ExchangeRate
		};
	}
}