/*
 * Copyright(c) RIB Software GmbH
 */

import { LazyInjectableInfo } from '@libs/platform/common';

export const LAZY_INJECTABLES: LazyInjectableInfo[] = [
    // Add lazy injectable services here when needed
    // Example for UiContainerSystemMainViewService:
    // LazyInjectableInfo.create('ui.container-system.MainViewService', MainViewServiceToken, async (context: ILazyInjectionContext) => {
    //     const importedModule = await import('@libs/ui/container-system');
    //     const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
    //     await platformModuleManagerService.initializeModule(importedModule);
    //     return context.doInstantiate ? new importedModule.UiContainerSystemMainViewService() : null;
    // }),
];
