/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';
import { ProcurementCommonUpdateVatGroupService } from '@libs/procurement/common';
import { IInvHeaderEntity, InvComplete } from '../model';
import { ProcurementInvoiceHeaderDataService } from '../header/procurement-invoice-header-data.service';

/**
 * Procurement invoice update vat group service
 */
@Injectable({
	providedIn: 'root'
})
export class ProcurementInvoiceUpdateVatGroupService extends ProcurementCommonUpdateVatGroupService<IInvHeaderEntity, InvComplete> {

	/**
	 * The constructor
	 * @protected
	 */
	protected constructor(public readonly invHeaderDataService: ProcurementInvoiceHeaderDataService) {
		super(invHeaderDataService, invHeaderDataService.getInternalModuleName());
	}

	/**
	 * Get contractName of customized recalculate logic after vatGroup updated
	 * @protected
	 */
	protected override getContractName(): string {
		return this.invHeaderDataService.getInternalModuleName();
	}
}