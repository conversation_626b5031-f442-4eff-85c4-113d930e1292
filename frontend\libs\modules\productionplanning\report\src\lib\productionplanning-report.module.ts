/*
 * Copyright(c) RIB Software GmbH
 */

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';

import { BusinessModuleRoute } from '@libs/ui/business-base';
import { UiCommonModule } from '@libs/ui/common';
import { PlatformCommonModule } from '@libs/platform/common';
import { GridComponent } from '@libs/ui/common';
import { FormsModule } from '@angular/forms';
import { ProductionplanningReportModuleInfo } from './model/productionplanning-report-module-info.class';
import { ProductionPlanningReportProductFilterComponent } from './components/product-filter.component';

const routes: Routes = [new BusinessModuleRoute(ProductionplanningReportModuleInfo.instance)];
@NgModule({
	declarations:[ProductionPlanningReportProductFilterComponent],
	imports: [CommonModule, RouterModule.forChild(routes), UiCommonModule, FormsModule, PlatformCommonModule,GridComponent],
	providers: [],
})
export class ProductionplanningReportModule {}
