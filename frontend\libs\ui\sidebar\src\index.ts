export * from './lib/ui-sidebar.module';

export * from './lib/model/class/sidebar-tab.class';
export * from './lib/model/ui-sidebar-module-info.class';
export * from './lib/model/circular-one.class';

export * from './lib/services/sidebar.service';
export * from './lib/services/sidebar-pin-settings.service';
export * from './lib/services/quickstart/quickstart-data-handling.service';

export * from './lib/components/chatbot-sidebar-tab/chatbot-sidebar-tab.component';
export * from './lib/components/favorites-sidebar/favorites-sidebar-tab/favorites-sidebar-tab.component';
export * from './lib/components/history-sidebar-tab/history-sidebar-tab.component';
export * from './lib/components/quickstart-sidebar-tab/quickstart-sidebar-tab.component';
export * from './lib/components/report-sidebar/report-sidebar-tab/report-sidebar-tab.component';
export * from './lib/components/wizards-sidebar-tab/wizards-sidebar-tab.component';
export * from './lib/components/search-sidebar-tab/search-sidebar-tab.component';

export * from './lib/model/enums/setting-type.enum';
export * from './lib/services/quickstart/quickstart-tabs-settings.service';
export * from './lib/model/interfaces/quickstart/quickstart-settings.interface';
export * from './lib/model/interfaces/quickstart/quickstart-module-tab.interface';
export * from './lib/model/interfaces/quickstart/quickstart-accordion-data.interface';
export * from './lib/model/interfaces/quickstart/quickstart-data.interface';
export * from './lib/model/interfaces/quickstart/quickstart-merged-setting.interface';
export * from './lib/model/interfaces/quickstart/quickstart-tab-settings.interface';
export * from './lib/model/interfaces/quickstart/quickstart-state.interface';

export * from './lib/model/interfaces/task/task-list-sorting-option.interface';

import { IApplicationModuleInfo } from '@libs/platform/common';
import { UiSidebarModuleInfo } from './lib/model/ui-sidebar-module-info.class';

/**
 * Returns the module info object for the ui.sidebar module.
 *
 * This function implements the {@link IApplicationModule.getModuleInfo} method.
 * Do not remove it.
 * It may be called by generated code.
 *
 * @return The singleton instance of the module info object.
 *
 * @see {@link IApplicationModule.getModuleInfo}
 */
export function getModuleInfo(): IApplicationModuleInfo {
    return UiSidebarModuleInfo.instance;
}

