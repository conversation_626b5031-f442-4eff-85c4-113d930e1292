import { Component, inject, OnInit } from '@angular/core';
import { IEntityIdentification } from '@libs/platform/common';
import { ProductionplanningReportReportDataService } from '../services/productionplanning-report-report-data.service';
import { ColumnDef, ILookupEvent, ILookupMultiSelectEvent } from '@libs/ui/common';
import { createLookup, FieldType } from '@libs/ui/common';
import { IPpsProductEntityGenerated, PpsSharedDrawingDialogLookupService } from '@libs/productionplanning/shared';
import { SourceWindowComponent } from '@libs/ui/business-base';
import { BasicsSharedPpsProductStatusLookupService } from '@libs/basics/shared';

/**
 * The interface for viewOptions,representing the filter criteria for report item info
 */
export interface IReportItemInfoViewOptions {
	ActivityId?: number;
	JobId?: number;
	DrawingId?: number;
	BundleId?: number;
	StatusIds?: number;
}
interface IFormData {
	selectedType: number;
}

@Component({
	selector: 'productionplanning-report-product-filter',
	templateUrl: './product-filter.component.html',
	styleUrl: './product-filter.component.css',
})
export class ProductionPlanningReportProductFilterComponent extends SourceWindowComponent<IPpsProductEntityGenerated> implements OnInit {
	protected containerUUID(): string | undefined {
		return 'cd94de40c47c4ac9adf6285250e9764f';
	}

	private readonly parentService = inject(ProductionplanningReportReportDataService);
	public PpsSharedDrawingDialogLookupService = inject(PpsSharedDrawingDialogLookupService);
	public selectedProductVersion!: IPpsProductEntityGenerated;

	public productSelected: IFormData = {
		selectedType: 0,
	};

	/**
	 * Angular lifecycle hook for component initialization.
	 */
	public override async ngOnInit(): Promise<void> {
		await super.ngOnInit();
	}

	/**
	 * Initializes the form configuration for the product filter.
	 */
	protected override initializeFormConfig(): void {
		this.formConfig = {
			formId: 'productionplanning.report.product.filter',
			showGrouping: false,
			addValidationAutomatically: false,
			groups: [
				{
					groupId: 'filter',
				},
			],
			rows: [
				//dummy lookups have been introduced for ActivityFk and LgmJobFk as the actual lookup sources are pending integration. These fields—ActivityFk and LgmJobFk—serve as key filters for the grid data, which will be dynamically displayed based on their selected values once fully implemented
				{
					groupId: 'filter',
					id: 'ActivityFk',
					label: { key: 'productionplanning.activity.entityActivity', text: 'Mounting Activity' },
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: PpsSharedDrawingDialogLookupService,
					}),
					model: 'ActivityFk',
					sortOrder: 1,
				},
				{
					groupId: 'filter',
					id: 'LgmJobFk',
					label: { key: 'project.costcodes.lgmJobFk', text: 'Job' },
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: PpsSharedDrawingDialogLookupService,
					}),
					model: 'LgmJobFk',
					sortOrder: 2,
				},
				{
					groupId: 'filter',
					id: 'EngDrawingFk',
					label: { key: 'productionplanning.drawing.entityDrawing', text: 'Drawing' },
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: PpsSharedDrawingDialogLookupService,
						descriptionMember: 'Description',
						events: [
							{
								name: 'onSelectedItemChanged',
								handler: (event) => {
									const e = event as ILookupEvent<IEntityIdentification, IFormData> | ILookupMultiSelectEvent<IEntityIdentification, IFormData>;
									if (e) {
										this.onLookupItemSelected(e);
									}
								},
							},
						],
					}),

					model: 'EngDrawingFk',
					sortOrder: 3,
				},
				{
					groupId: 'filter',
					id: 'TrsProductBundleFk',
					label: { key: 'transportplanning.bundle.entityBundle', text: 'Bundle' },
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: PpsSharedDrawingDialogLookupService,
					}),
					model: 'TrsProductBundleFk',
					sortOrder: 4,
				},
				{
					groupId: 'filter',
					id: 'ProductStatusFk',
					label: { key: 'cloud.common.entityStatus', text: 'Status' },
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: BasicsSharedPpsProductStatusLookupService,
						displayMember: 'DescriptionInfo'
					}),
					//TODO dropdown not supporting yet from framework, so using lookup
					model: 'ProductStatusFk',
					sortOrder: 5,
				},
			],
		};
	}

	/**
	 * Initializes the grid columns configuration for the product grid.
	 */
	protected override async createGridConfig(): Promise<void> {
		// TODO: Attempting to dynamically set columns using the following approach:
		// const layoutConfiguration = modelLookupProvider.entities[0].config.layoutConfiguration;
		// The framework currently doesn't support this. It seems modifications are needed in the entity-info.class.ts file
		// to enable dynamic column setting. Currently, columns are passed statically.
		// We have raised a ticket for this issue with Florian: https://rib-40.atlassian.net/browse/DEV-23801

		const columns: ColumnDef<IPpsProductEntityGenerated>[] = [
			// Product group
			{ id: 'Code', label: { text: 'Code' }, type: FieldType.Description, model: 'Code', readonly: true, visible: true, sortable: true },
			{ id: 'Descriptioninfo', label: { text: 'Description' }, type: FieldType.Description, model: 'Descriptioninfo', readonly: true, visible: true, sortable: true },
			{ id: 'ProductStatusFk', label: { text: 'Product Status' }, type: FieldType.Integer, model: 'ProductStatusFk', readonly: true, visible: true, sortable: true },
			{ id: 'ProductDescriptionFk', label: { text: 'Product Description' }, type: FieldType.Integer, model: 'ProductDescriptionFk', readonly: true, visible: true, sortable: true },
			{ id: 'ProjectId', label: { text: 'Project Id' }, type: FieldType.Integer, model: 'ProjectId', readonly: true, visible: true, sortable: true },
			{ id: 'EngDrawingFk', label: { text: 'Engineering Drawing' }, type: FieldType.Integer, model: 'EngDrawingFk', readonly: true, visible: true, sortable: true },
			{ id: 'MaterialFk', label: { text: 'Material' }, type: FieldType.Integer, model: 'MaterialFk', readonly: true, visible: true, sortable: true },
			{ id: 'LgmJobFk', label: { text: 'LGM Job' }, type: FieldType.Integer, model: 'LgmJobFk', readonly: true, visible: true, sortable: true },
			{ id: 'ExternalCode', label: { text: 'External Code' }, type: FieldType.Description, model: 'ExternalCode', readonly: true, visible: true, sortable: true },
			{ id: 'PpsStrandPatternFk', label: { text: 'Strand Pattern' }, type: FieldType.Integer, model: 'PpsStrandPatternFk', readonly: true, visible: true, sortable: true },
			{ id: 'IsLive', label: { text: 'Active' }, type: FieldType.Boolean, model: 'IsLive', readonly: true, visible: true, sortable: true },
			{ id: 'Guid', label: { text: 'Guid' }, type: FieldType.Description, model: 'Guid', readonly: true, visible: true, sortable: true },

			// Production group
			{ id: 'ProductionSetFk', label: { text: 'Production Set' }, type: FieldType.Integer, model: 'ProductionSetFk', readonly: true, visible: true, sortable: true },
			{ id: 'TrsProductBundleFk', label: { text: 'Product Bundle' }, type: FieldType.Integer, model: 'TrsProductBundleFk', readonly: true, visible: true, sortable: true },
			{ id: 'PrjLocationFk', label: { text: 'Project Location' }, type: FieldType.Integer, model: 'PrjLocationFk', readonly: true, visible: true, sortable: true },
			{ id: 'PuPrjLocationFk', label: { text: 'PU Project Location' }, type: FieldType.Integer, model: 'PuPrjLocationFk', readonly: true, visible: true, sortable: true },
			{ id: 'UnitPrice', label: { text: 'Unit Price' }, type: FieldType.Integer, model: 'UnitPrice', readonly: true, visible: true, sortable: true },
			{ id: 'BillQuantity', label: { text: 'Bill Quantity' }, type: FieldType.Integer, model: 'BillQuantity', readonly: true, visible: true, sortable: true },
			{ id: 'BasUomBillFk', label: { text: 'UoM Bill' }, type: FieldType.Integer, model: 'BasUomBillFk', readonly: true, visible: true, sortable: true },
			{ id: 'PlanQuantity', label: { text: 'Plan Quantity' }, type: FieldType.Integer, model: 'PlanQuantity', readonly: true, visible: true, sortable: true },
			{ id: 'BasUomPlanFk', label: { text: 'UoM Plan' }, type: FieldType.Integer, model: 'BasUomPlanFk', readonly: true, visible: true, sortable: true },
			{ id: 'ItemFk', label: { text: 'Item' }, type: FieldType.Integer, model: 'ItemFk', readonly: true, visible: true, sortable: true },
			{ id: 'ProductionOrder', label: { text: 'Production Order' }, type: FieldType.Description, model: 'ProductionOrder', readonly: true, visible: true, sortable: true },
			{ id: 'Reproduced', label: { text: 'Reproduced' }, type: FieldType.Boolean, model: 'Reproduced', readonly: true, visible: true, sortable: true },
			{ id: 'PrjStockFk', label: { text: 'Project Stock' }, type: FieldType.Integer, model: 'PrjStockFk', readonly: true, visible: true, sortable: true },
			{ id: 'PrjStockLocationFk', label: { text: 'Project Stock Location' }, type: FieldType.Integer, model: 'PrjStockLocationFk', readonly: true, visible: true, sortable: true },
			{ id: 'ProductionTime', label: { text: 'Production Time' }, type: FieldType.Integer, model: 'ProductionTime', readonly: true, visible: true, sortable: true },
			{ id: 'PpsProcessFk', label: { text: 'Process' }, type: FieldType.Integer, model: 'PpsProcessFk', readonly: true, visible: true, sortable: true },
			{ id: 'PpsProductionSetSubFk', label: { text: 'Production Set Sub' }, type: FieldType.Integer, model: 'PpsProductionSetSubFk', readonly: true, visible: true, sortable: true },
			{ id: 'FabriCode', label: { text: 'Fabri Code' }, type: FieldType.Description, model: 'FabriCode', readonly: true, visible: true, sortable: true },
			{ id: 'FabriExternalCode', label: { text: 'Fabri External Code' }, type: FieldType.Description, model: 'FabriExternalCode', readonly: true, visible: true, sortable: true },
			{ id: 'ProdPlaceFk', label: { text: 'Production Place' }, type: FieldType.Integer, model: 'ProdPlaceFk', readonly: true, visible: true, sortable: true },

			// Dimensions group
			{ id: 'Length', label: { text: 'Length' }, type: FieldType.Integer, model: 'Length', readonly: true, visible: true, sortable: true },
			{ id: 'BasUomLengthFk', label: { text: 'UoM Length' }, type: FieldType.Integer, model: 'BasUomLengthFk', readonly: true, visible: true, sortable: true },
			{ id: 'Width', label: { text: 'Width' }, type: FieldType.Integer, model: 'Width', readonly: true, visible: true, sortable: true },
			{ id: 'BasUomWidthFk', label: { text: 'UoM Width' }, type: FieldType.Integer, model: 'BasUomWidthFk', readonly: true, visible: true, sortable: true },
			{ id: 'Height', label: { text: 'Height' }, type: FieldType.Integer, model: 'Height', readonly: true, visible: true, sortable: true },
			{ id: 'BasUomHeightFk', label: { text: 'UoM Height' }, type: FieldType.Integer, model: 'BasUomHeightFk', readonly: true, visible: true, sortable: true },
			{ id: 'Area', label: { text: 'Area' }, type: FieldType.Integer, model: 'Area', readonly: true, visible: true, sortable: true },
			{ id: 'Area2', label: { text: 'Area2' }, type: FieldType.Integer, model: 'Area2', readonly: true, visible: true, sortable: true },
			{ id: 'Area3', label: { text: 'Area3' }, type: FieldType.Integer, model: 'Area3', readonly: true, visible: true, sortable: true },
			{ id: 'BasUomAreaFk', label: { text: 'UoM Area' }, type: FieldType.Integer, model: 'BasUomAreaFk', readonly: true, visible: true, sortable: true },
			{ id: 'Volume', label: { text: 'Volume' }, type: FieldType.Integer, model: 'Volume', readonly: true, visible: true, sortable: true },
			{ id: 'Volume2', label: { text: 'Volume2' }, type: FieldType.Integer, model: 'Volume2', readonly: true, visible: true, sortable: true },
			{ id: 'Volume3', label: { text: 'Volume3' }, type: FieldType.Integer, model: 'Volume3', readonly: true, visible: true, sortable: true },
			{ id: 'BasUomVolumeFk', label: { text: 'UoM Volume' }, type: FieldType.Integer, model: 'BasUomVolumeFk', readonly: true, visible: true, sortable: true },

			// Properties group
			{ id: 'IsolationVolume', label: { text: 'Isolation Volume' }, type: FieldType.Integer, model: 'IsolationVolume', readonly: true, visible: true, sortable: true },
			{ id: 'ConcreteVolume', label: { text: 'Concrete Volume' }, type: FieldType.Integer, model: 'ConcreteVolume', readonly: true, visible: true, sortable: true },
			{ id: 'ConcreteQuality', label: { text: 'Concrete Quality' }, type: FieldType.Description, model: 'ConcreteQuality', readonly: true, visible: true, sortable: true },
			{ id: 'Weight', label: { text: 'Weight' }, type: FieldType.Integer, model: 'Weight', readonly: true, visible: true, sortable: true },
			{ id: 'Weight2', label: { text: 'Weight2' }, type: FieldType.Integer, model: 'Weight2', readonly: true, visible: true, sortable: true },
			{ id: 'Weight3', label: { text: 'Weight3' }, type: FieldType.Integer, model: 'Weight3', readonly: true, visible: true, sortable: true },
			{ id: 'ActualWeight', label: { text: 'Actual Weight' }, type: FieldType.Integer, model: 'ActualWeight', readonly: true, visible: true, sortable: true },
			{ id: 'BasUomWeightFk', label: { text: 'UoM Weight' }, type: FieldType.Integer, model: 'BasUomWeightFk', readonly: true, visible: true, sortable: true },

			// Transport group
			{ id: 'TrsRequisitionFk', label: { text: 'Transport Requisition' }, type: FieldType.Integer, model: 'TrsRequisitionFk', readonly: true, visible: true, sortable: true },
			{ id: 'TrsRequisitionDate', label: { text: 'Requisition Date' }, type: FieldType.DateUtc, model: 'TrsRequisitionDate', readonly: true, visible: true, sortable: true },

			// User defined text group
			{ id: 'Userdefined1', label: { text: 'Userdefined1' }, type: FieldType.Description, model: 'Userdefined1', readonly: true, visible: true, sortable: true },
			{ id: 'Userdefined2', label: { text: 'Userdefined2' }, type: FieldType.Description, model: 'Userdefined2', readonly: true, visible: true, sortable: true },
			{ id: 'Userdefined3', label: { text: 'Userdefined3' }, type: FieldType.Description, model: 'Userdefined3', readonly: true, visible: true, sortable: true },
			{ id: 'Userdefined4', label: { text: 'Userdefined4' }, type: FieldType.Description, model: 'Userdefined4', readonly: true, visible: true, sortable: true },
			{ id: 'Userdefined5', label: { text: 'Userdefined5' }, type: FieldType.Description, model: 'Userdefined5', readonly: true, visible: true, sortable: true },
			{ id: 'UserdefinedByProddesc1', label: { text: 'Userdefined By Proddesc1' }, type: FieldType.Description, model: 'UserdefinedByProddesc1', readonly: true, visible: true, sortable: true },
			{ id: 'UserdefinedByProddesc2', label: { text: 'Userdefined By Proddesc2' }, type: FieldType.Description, model: 'UserdefinedByProddesc2', readonly: true, visible: true, sortable: true },
			{ id: 'UserdefinedByProddesc3', label: { text: 'Userdefined By Proddesc3' }, type: FieldType.Description, model: 'UserdefinedByProddesc3', readonly: true, visible: true, sortable: true },
			{ id: 'UserdefinedByProddesc4', label: { text: 'Userdefined By Proddesc4' }, type: FieldType.Description, model: 'UserdefinedByProddesc4', readonly: true, visible: true, sortable: true },
			{ id: 'UserdefinedByProddesc5', label: { text: 'Userdefined By Proddesc5' }, type: FieldType.Description, model: 'UserdefinedByProddesc5', readonly: true, visible: true, sortable: true },
		];
		this.gridColumns = columns;
	}

	/**
	 * Loads grid data using the current filter values.
	 */
	public override loadGridData(): void {
		const filterPayload = {
			ActivityId: this.parentService.getSelectedEntity()?.ActivityFk,
			JobId: 0,
			DrawingId: 0,
			BundleId: 0,
			StatusIds: 0,
		};
		super.loadGridData('productionplanning/common/product/getproductsbyfilter', filterPayload);
	}

	/**
	 * Handles selection change event from the grid.
	 * @param selectedItems The selected product entities.
	 */
	public selectionChanged(selectedItems: IPpsProductEntityGenerated[]) {
		if (selectedItems.length > 0) {
			this.selectedProductVersion = selectedItems[0];
		}
	}

	/**
	 * Handles lookup item selection event from the form.
	 * @param e The lookup event.
	 */
	protected override onLookupItemSelected(
		e: ILookupEvent<IEntityIdentification, IFormData> | ILookupMultiSelectEvent<IEntityIdentification, IFormData>
	): void {
		if (e.context.lookupInput?.selectedItem?.Id) {
			this.productSelected.selectedType = e.context.lookupInput.selectedItem.Id;
			this.loadGridData();
		}
	}
}
