import { LazyInjectable, PlatformLazyInjectorService } from '@libs/platform/common';
import { UiCircularOneToken, UiCircularTwoToken, IUiCircularOne } from '@libs/ui/interfaces';
import { Injector } from '@angular/core';

@LazyInjectable({
	token: UiCircularOneToken
})
export class CircularOne implements IUiCircularOne {

	public constructor(injector: Injector) {
		this.lazyInjector = injector.get(PlatformLazyInjectorService);
	}

	private readonly lazyInjector: PlatformLazyInjectorService;

	public test() {
		this.lazyInjector.inject(UiCircularTwoToken).then(c => c.outputOther());
	}

	public output() {
		console.log('Circular One!');
	}
}