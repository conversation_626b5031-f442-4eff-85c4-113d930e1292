/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import { ReqHeaderCompleteEntity } from '../model/entities/requisition-complete-entity.class';
import { ProcurementCommonUpdateVatGroupService } from '@libs/procurement/common';
import { IReqHeaderEntity } from '../model/entities/reqheader-entity.interface';
import { ProcurementRequisitionHeaderDataService } from './requisition-header-data.service';

/**
 * Procurement requisition update vat group service
 */
@Injectable({
	providedIn: 'root'
})
export class ProcurementRequisitionUpdateVatGroupService extends ProcurementCommonUpdateVatGroupService<IReqHeaderEntity, ReqHeaderCompleteEntity> {

	/**
	 * The constructor
	 * @protected
	 */
	protected constructor() {
		const dataService = inject(ProcurementRequisitionHeaderDataService);
		super(dataService, dataService.getInternalModuleName());
	}
}