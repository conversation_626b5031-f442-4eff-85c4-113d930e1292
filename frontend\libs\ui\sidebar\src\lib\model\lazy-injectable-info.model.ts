/*
 * Copyright(c) RIB Software GmbH
 */

import { LazyInjectableInfo, ILazyInjectionContext, PlatformModuleManagerService } from '@libs/platform/common';
import { UiCircularOneToken } from '@libs/ui/interfaces';

export const LAZY_INJECTABLES: LazyInjectableInfo[] = [
    LazyInjectableInfo.create('ui.sidebar.CircularOne', UiCircularOneToken, async (context: ILazyInjectionContext) => {
        const importedModule = await import('@libs/ui/sidebar');
        const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
        await platformModuleManagerService.initializeModule(importedModule);
        return context.doInstantiate ? new importedModule.CircularOne(context.injector) : null;
    }),
];
