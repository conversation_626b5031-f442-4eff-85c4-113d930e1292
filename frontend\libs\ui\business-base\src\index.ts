/*
 * Copyright(c) RIB Software GmbH
 */

export * from './lib/ui-business-base.module';
export * from './lib/model/ui-business-base-module-info.class';

export {
	BusinessModuleInfoBase,
	isBusinessModuleInfo
 } from './lib/model/business-module-info-base.class';
export { BusinessModuleRoute } from './lib/model/business-module-route.class';
export { IBusinessModuleAddOn } from './lib/model/business-module-add-on.interface';
export * from './lib/model/translation-container-info.interface';

export * from './lib/entities/index';

export  * from './lib/entities/services/entity-container-menulist-helper.service';
export * from './lib/entities/model/entity-facade.interface';

import { IApplicationModuleInfo } from '@libs/platform/common';
import { UiBusinessBaseModuleInfo } from './lib/model/ui-business-base-module-info.class';

/**
 * Returns the module info object for the ui.business-base module.
 *
 * This function implements the {@link IApplicationModule.getModuleInfo} method.
 * Do not remove it.
 * It may be called by generated code.
 *
 * @return The singleton instance of the module info object.
 *
 * @see {@link IApplicationModule.getModuleInfo}
 */
export function getModuleInfo(): IApplicationModuleInfo {
    return UiBusinessBaseModuleInfo.instance;
}
